dependencies {
    implementation 'com.fasterxml.jackson.core:jackson-annotations:2.13.4.2'
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.13.4.2'
    implementation 'com.fasterxml.jackson.core:jackson-core:2.13.3'

    implementation 'org.apache.httpcomponents:httpcore:4.4.15'
    implementation 'org.apache.httpcomponents:httpclient:4.5.13'
    implementation 'io.github.openfeign:feign-httpclient:11.9.1'

    api 'com.homethy:homethy-configcenter-client:4.5-spring-SNAPSHOT'
    api 'com.homethy:homethy-configcenter-model:4.5-spring-SNAPSHOT'
    api 'com.homethy:homethy-util-redis:4.5.2-spring-SNAPSHOT'
    implementation('com.lofty:site-searcher-client:4.6-SNAPSHOT') {
        exclude group: 'com.baomidou', module: 'mybatis-plus-annotation'
    }
    implementation 'org.springframework.data:spring-data-redis:3.2.4'

    implementation 'mysql:mysql-connector-java:8.0.33'
    implementation 'org.mybatis.spring.boot:mybatis-spring-boot-starter:3.0.3'
    api 'com.homethy.microservice:homethy-agent-client:4.21-SNAPSHOT'
    api 'org.springframework.cloud:spring-cloud-starter-openfeign'
    api 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'com.google.guava:guava:33.0.0-jre'
    implementation 'org.apache.commons:commons-lang3:3.9'
    implementation 'commons-beanutils:commons-beanutils:1.9.4'
    implementation 'cn.hutool:hutool-all:5.8.25'
    api 'com.homethy.logback:logback-PII:4.12-SNAPSHOT'
    api 'org.perf4j:perf4j:0.9.16'
    api 'agent.search:agent-search-client:4.16_org_redesign-SNAPSHOT'
    api 'com.homethy:homethy-util-global-config:3.3.0-SNAPSHOT'
    api 'com.homethy.microservice:homethy-lead-client:4.22-SNAPSHOT'
    implementation('com.homethy.microservice:microservice-config:4.5-spring-SNAPSHOT') {
        exclude group: 'org.slf4j', module: 'slf4j-api'
    }
    api('com.homethy.lead.timeline:homethy-lead-timeline-client:4.21-SNAPSHOT') {
        exclude group: 'org.slf4j', module: 'slf4j-api'
    }
    testImplementation('org.springframework.boot:spring-boot-starter-test:2.7.2') {
        exclude module: 'commons-logging'
    }
}