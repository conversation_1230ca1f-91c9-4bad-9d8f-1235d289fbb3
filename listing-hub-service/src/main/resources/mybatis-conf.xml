<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration
        PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>

    <settings>

        <!-- Global mapper enable cache -->
        <setting name="cacheEnabled" value="true"/>

        <!-- When querying, turn off instant loading of associated objects to improve performance -->
        <setting name="lazyLoadingEnabled" value="true"/>

        <!-- Set the form of loading the associated object, here is the on-demand loading field (the loading field is specified by SQL), and all the fields of the associated table will not be loaded to improve performance -->
        <setting name="aggressiveLazyLoading" value="false"/>

        <!-- For unknown SQL queries, different result sets are allowed to be returned to achieve universal effects -->
        <setting name="multipleResultSetsEnabled" value="true"/>

        <!-- Allow the use of column labels instead of column names -->
        <setting name="useColumnLabel" value="true"/>

        <!-- Allows the use of custom primary key values (such as the UUID 32-bit code generated by the program as the key value), the PK generation strategy of the data table will be overwritten -->
        <setting name="useGeneratedKeys" value="true"/>

        <!-- Give the nested resultMap field-attribute mapping support -->
        <setting name="autoMappingBehavior" value="FULL"/>

        <!-- No batch update operation -->
        <setting name="defaultExecutorType" value="REUSE"/>

        <!-- The database has timed out if there is no response for more than 25000 seconds -->
        <setting name="defaultStatementTimeout" value="25000"/>

        <!-- Whether to enable automatic camel case mapping, that is, from the classic database column name A_COLUMN to the classic Java attribute name aColumn
            The similar mapping. -->
        <setting name="mapUnderscoreToCamelCase" value="true"/>
    </settings>

</configuration>
