package com.homethy.enums;

public enum WaterSupplyType {
  MAINLINE_SUPPLY(0, "Mains Supply"),
  W<PERSON>LS(1, "Wells"),
  BOREHOLES(2, "Boreholes"),
  SPRINGS(3, "Springs"),
  OTHER(4, "Other");

  private final int code;
  private final String description;

  WaterSupplyType(int code, String description) {
    this.code = code;
    this.description = description;
  }

  public int getCode() {
    return code;
  }

  public String getDescription() {
    return description;
  }

  public static WaterSupplyType fromCode(int code) {
    for (WaterSupplyType supplyType : WaterSupplyType.values()) {
      if (supplyType.getCode() == code) {
        return supplyType;
      }
    }
    throw new IllegalArgumentException("Invalid code for WaterSupplyType: " + code);
  }
}
