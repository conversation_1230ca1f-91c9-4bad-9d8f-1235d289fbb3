package com.homethy.enums;

public enum TrueOrFalse {
  FALSE(0, "false"),
  TRUE(1, "true");

  private final int code;
  private final String description;

  TrueOrFalse(int code, String description) {
    this.code = code;
    this.description = description;
  }

  public int getCode() {
    return code;
  }

  public String getDescription() {
    return description;
  }

  public static TrueOrFalse fromCode(int code) {
    for (TrueOrFalse status : TrueOrFalse.values()) {
      if (status.getCode() == code) {
        return status;
      }
    }
    throw new IllegalArgumentException("Invalid code for TrueOrFalse: " + code);
  }
}
