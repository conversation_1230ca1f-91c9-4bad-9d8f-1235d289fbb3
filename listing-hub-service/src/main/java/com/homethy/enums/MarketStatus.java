package com.homethy.enums;

public enum MarketStatus {
  SELLING(0, "selling"),
  SOLD(1, "sold"),
  EXPIRED(2, "expired");

  private final int code;
  private final String description;

  MarketStatus(int code, String description) {
    this.code = code;
    this.description = description;
  }

  public int getCode() {
    return code;
  }

  public String getDescription() {
    return description;
  }

  public static MarketStatus fromCode(int code) {
    for (MarketStatus status : MarketStatus.values()) {
      if (status.getCode() == code) {
        return status;
      }
    }
    throw new IllegalArgumentException("Invalid code for MarketStatus: " + code);
  }

  public static MarketStatus fromListingStatus(int listingStatus) {
    if (ListingStatus.WITHDRAWN.getCode() == listingStatus) {
      return MarketStatus.EXPIRED;
    } else if (ListingStatus.SOLD.getCode() == listingStatus
        || ListingStatus.LET.getCode() == listingStatus) {
      return MarketStatus.SOLD;
    }
    return MarketStatus.SELLING;
  }
}
