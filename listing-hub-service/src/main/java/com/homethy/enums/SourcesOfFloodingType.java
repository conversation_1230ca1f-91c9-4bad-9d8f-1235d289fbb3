package com.homethy.enums;

public enum SourcesOfFloodingType {
  RIVER(0, "River"),
  LAKE(1, "Lake"),
  SEA(2, "Sea"),
  RESERVOIR(3, "Reservoir"),
  GROUNDWATER(4, "Groundwater"),
  <PERSON>THER(5, "Other");

  private final int code;
  private final String description;

  SourcesOfFloodingType(int code, String description) {
    this.code = code;
    this.description = description;
  }

  public int getCode() {
    return code;
  }

  public String getDescription() {
    return description;
  }

  public static SourcesOfFloodingType fromCode(int code) {
    for (SourcesOfFloodingType floodingType : SourcesOfFloodingType.values()) {
      if (floodingType.getCode() == code) {
        return floodingType;
      }
    }
    throw new IllegalArgumentException("Invalid code for SourcesOfFloodingType: " + code);
  }
}
