package com.homethy.enums;

public enum ListingDisplayType {
  NO_WEBSITE("no", "no website"),
  MY_WEBSITE("my", "my website"),
  TEAM_WEBSITE("all", "my team/group's website");

  private final String code;
  private final String description;

  ListingDisplayType(String code, String description) {
    this.code = code;
    this.description = description;
  }

  public String getCode() {
    return code;
  }

  public String getDescription() {
    return description;
  }

  public static ListingDisplayType fromCode(String code) {
    for (ListingDisplayType listingDisplayType : ListingDisplayType.values()) {
      if (listingDisplayType.getCode().equals(code)) {
        return listingDisplayType;
      }
    }
    throw new IllegalArgumentException("Invalid code for ListingDisplayType: " + code);
  }
}
