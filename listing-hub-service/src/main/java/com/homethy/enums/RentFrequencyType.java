package com.homethy.enums;

public enum RentFrequencyType {
  MONTHLY(0, "Monthly"),
  YEARLY(1, "Yearly");

  private final int code;
  private final String description;

  RentFrequencyType(int code, String description) {
    this.code = code;
    this.description = description;
  }

  public int getCode() {
    return code;
  }

  public String getDescription() {
    return description;
  }

  public static RentFrequencyType fromCode(int code) {
    for (RentFrequencyType status : RentFrequencyType.values()) {
      if (status.getCode() == code) {
        return status;
      }
    }
    throw new IllegalArgumentException("Invalid code for RentFrequencyType: " + code);
  }

  /**
   * RightMove shared_ownership_rent_frequency
   * 1 Yearly, 4 Quarterly, 12 Monthly, 52 Weekly, 365 Daily
   */
  public static Integer convertToRightMoveShardOwnRentFrequency(int code) {
    if (RentFrequencyType.MONTHLY.code == code) {
      return 12;
    }
    return 1;
  }
}
