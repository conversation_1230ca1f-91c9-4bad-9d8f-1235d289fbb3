package com.homethy.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public enum ParkingType {
  DRIVE(0, "Drive"),
  GARAGE(1, "Garage"),
  STREET_PARKING(2, "Street parking"),
  COMMUNAL_CAR_PARK(3, "Communal car park"),
  NO_PARKING_AVAILABLE(4, "No parking available");

  private final int code;
  private final String description;

  ParkingType(int code, String description) {
    this.code = code;
    this.description = description;
  }

  public int getCode() {
    return code;
  }

  public String getDescription() {
    return description;
  }

  public static ParkingType fromCode(int code) {
    for (ParkingType parkingType : ParkingType.values()) {
      if (parkingType.getCode() == code) {
        return parkingType;
      }
    }
    throw new IllegalArgumentException("Invalid code for ParkingType: " + code);
  }

  /**
   * RightMove parking type
   * 13 Allocated, 14 Communal, 15 Covered, 16 Garage, 17 Driveway, 18 Gated,
   * 19 Off Street, 20 On Street, 21 Rear, 22 Permit, 23 Private, 24 Residents
   */
  public static Set<Integer> convertToRightMoveParkingType(String codes) {
    if (StringUtils.isBlank(codes)) {
      return null;
    }
    Set<Integer> result = new HashSet<>();
    Arrays.stream(codes.split(",")).forEach(i -> {
      int code = Integer.parseInt(i);
      if(ParkingType.DRIVE.code == code) {
        // Driveway, Off-Street, Private
        result.add(17);
        result.add(19);
        result.add(23);
      } else if (ParkingType.GARAGE.code == code) {
        // Covered, Garage, Private, Gated
        result.add(15);
        result.add(16);
        result.add(18);
        result.add(23);
      } else if (ParkingType.STREET_PARKING.code == code) {
        // On-Street, Permit
        result.add(20);
        result.add(22);
      } else if (ParkingType.COMMUNAL_CAR_PARK.code == code) {
        // Allocated, Communal, Residents, Gated
        result.add(13);
        result.add(14);
        result.add(24);
        result.add(18);
      }
    });
    return result;
  }

  public static List<String> convertToZooplaType(int code) {
    return convertToZooplaType(fromCode(code));
  }

  public static List<String> convertToZooplaType(ParkingType type) {
    return switch (type) {
      case DRIVE -> List.of(
          "driveway_private",
          "driveway_shared",
          "off_street_parking",
          "rear_of_property",
          "gated_parking",
          "ev_charging_private",
          "ev_charging_shared"
      );
      case GARAGE -> List.of(
          "single_garage",
          "garage",
          "garage_bloc",
          "garage_carport",
          "garage_detached",
          "garage_integral",
          "undercroft",
          "underground",
          "underground_parking_allocated_space"
      );
      case STREET_PARKING -> List.of("street_parking_permit_required",
          "street_parking_permit_not_required");
      case COMMUNAL_CAR_PARK -> List.of(
          "communal_car_park_allocated_space",
          "communal_car_park_no_allocated_space",
          "residents_parking",
          "gated_parking",
          "disabled_parking_available",
          "disabled_parking_not_available",
          "underground_parking_no_allocated_space"
      );
      case NO_PARKING_AVAILABLE -> List.of(
          "no_parking_available",
          "other"
      );
    };
  }
}
