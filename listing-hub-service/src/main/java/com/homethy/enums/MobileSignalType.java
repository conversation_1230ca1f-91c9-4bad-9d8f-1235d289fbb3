package com.homethy.enums;

public enum MobileSignalType {
  _2G(0, "2G"),
  _3G(1, "3G"),
  _4G(2, "4G"),
  _5G(3, "5G"),
  WIFI_CALLING(4, "wi-fi calling");

  private final int code;
  private final String description;

  MobileSignalType(int code, String description) {
    this.code = code;
    this.description = description;
  }

  public int getCode() {
    return code;
  }

  public String getDescription() {
    return description;
  }

  public static MobileSignalType fromCode(int code) {
    for (MobileSignalType signalType : MobileSignalType.values()) {
      if (signalType.getCode() == code) {
        return signalType;
      }
    }
    throw new IllegalArgumentException("Invalid code for MobileSignalType: " + code);
  }
}
