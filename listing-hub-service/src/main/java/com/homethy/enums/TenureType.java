package com.homethy.enums;

public enum TenureType {
  LEASEHOLD(0, "Leasehold"),
  LEASEHOLD_SHARE_OF_FREEHOLD(1, "Leasehold-Share of freehold"),
  FREEHOLD(2, "Freehold"),
  COMMONHOLD(3, "Commonhold");

  private final int code;
  private final String description;

  TenureType(int code, String description) {
    this.code = code;
    this.description = description;
  }

  public int getCode() {
    return code;
  }

  public String getDescription() {
    return description;
  }

  public static TenureType fromCode(int code) {
    for (TenureType tenureType : TenureType.values()) {
      if (tenureType.getCode() == code) {
        return tenureType;
      }
    }
    throw new IllegalArgumentException("Invalid code for TenureType: " + code);
  }

  /**
   * RightMove tenure type
   * 1 Freehold, 2 Leasehold, 3 Feudal, 4 Commonhold, 5 Share of Freehold
   */
  public static Integer convertToRightMoveTenureType(int code) {
    if (TenureType.LEASEHOLD.code == code) {
      return 2;
    } else if (TenureType.LEASEHOLD_SHARE_OF_FREEHOLD.code == code) {
      return 5;
    } else if (TenureType.FREEHOLD.code == code) {
      return 1;
    }
    return 4;
  }

  public static String convertToZooplaType(TenureType type) {
    return switch (type) {
      case LEASEHOLD -> "leasehold";
      case LEASEHOLD_SHARE_OF_FREEHOLD -> "share_of_freehold";
      case FREEHOLD -> "freehold";
      case COMMONHOLD -> "commonhold";
    };
  }

  public static String convertToZooplaType(int code) {
    return convertToZooplaType(fromCode(code));
  }
}
