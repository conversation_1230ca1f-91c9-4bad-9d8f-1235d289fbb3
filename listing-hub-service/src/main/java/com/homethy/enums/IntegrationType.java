package com.homethy.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Description
 * @create 2024-10-30 9:52
 */
@Getter
public enum IntegrationType {

  RIGHT_MOVE(1, "RightMove", 39),
  ZOOPLA(2, "<PERSON><PERSON><PERSON>", 38),
  ;

  private final int code;
  private final String description;
  private final int realSource;

  private IntegrationType(int code, String description, int realSource) {
    this.code = code;
    this.description = description;
    this.realSource = realSource;
  }

  public static IntegrationType fromCode(int code) {
    for (IntegrationType integrationType : IntegrationType.values()) {
      if (integrationType.getCode() == code) {
        return integrationType;
      }
    }
    throw new IllegalArgumentException("Invalid code for IntegrationType: " + code);
  }

  public static IntegrationType fromName(String name) {
    for (IntegrationType integrationType : IntegrationType.values()) {
      if (integrationType.name().equals(name)) {
        return integrationType;
      }
    }
    throw new IllegalArgumentException("Invalid code for IntegrationType: " + name);
  }
}
