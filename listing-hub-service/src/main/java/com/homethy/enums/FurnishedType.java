package com.homethy.enums;

public enum FurnishedType {
  UNFURNISHED(0, "Unfurnished"),
  FURNISHED(1, "Furnished"),
  OPTIONAL(2, "Optional");

  private final int code;
  private final String description;

  FurnishedType(int code, String description) {
    this.code = code;
    this.description = description;
  }

  public int getCode() {
    return code;
  }

  public String getDescription() {
    return description;
  }

  public static FurnishedType fromCode(int code) {
    for (FurnishedType status : FurnishedType.values()) {
      if (status.getCode() == code) {
        return status;
      }
    }
    throw new IllegalArgumentException("Invalid code for FurnishedType: " + code);
  }

  /**
   * RightMove Furnished Type
   * 0 Furnished, 1 Part-furnished, 2 Unfurnished, 4 Furnished/Unfurnished
   */
  public static Integer convertToRightMoveFurnishedType(int code) {
    if (FurnishedType.UNFURNISHED.code == code) {
      return 2;
    } else if (FurnishedType.FURNISHED.code == code) {
      return 0;
    }
    return 4;
  }

  public static String convertToZooplaType(FurnishedType type) {
    return switch (type) {
      case FURNISHED -> "furnished";
      case UNFURNISHED -> "unfurnished";
      case OPTIONAL -> "furnished_or_unfurnished";
    };
  }

  public static String convertToZooplaType(int code) {
    return convertToZooplaType(fromCode(code));
  }
}
