package com.homethy.enums;

import java.util.List;

public enum ElectricitySupplyType {
  MAINS_SUPPLY(0, "Mains Supply"),
  WIND_TURBINES(1, "Wind turbines"),
  SOLAR_PV_PANELS(2, "Solar PV panels"),
  GENERATOR_PRIVATE_SUPPLY(3, "Generator/private supply"),
  OTHER(4, "Other");

  private final int code;
  private final String description;

  ElectricitySupplyType(int code, String description) {
    this.code = code;
    this.description = description;
  }

  public int getCode() {
    return code;
  }

  public String getDescription() {
    return description;
  }

  public static ElectricitySupplyType fromCode(int code) {
    for (ElectricitySupplyType supplyType : ElectricitySupplyType.values()) {
      if (supplyType.getCode() == code) {
        return supplyType;
      }
    }
    throw new IllegalArgumentException("Invalid code for ElectricitySupplyType: " + code);
  }

  public static List<String> convertToZooplaType(int code) {
    return convertToZooplaType(fromCode(code));
  }

  public static List<String> convertToZooplaType(ElectricitySupplyType type) {
    return switch (type) {
      case MAINS_SUPPLY -> List.of("mains_supply");
      case WIND_TURBINES -> List.of("wind_turbine");
      case SOLAR_PV_PANELS -> List.of("solar_pv_panels");
      case GENERATOR_PRIVATE_SUPPLY -> List.of("generator", "private_supply");
      case OTHER -> List.of("other");
    };
  }
}
