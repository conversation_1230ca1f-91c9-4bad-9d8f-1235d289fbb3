package com.homethy.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

public enum HeatingType {
  ELECTRIC_CENTRAL(0, "Electric central"),
  ROOM_HEATING(1, "Room heating"),
  COMMUNAL_HEATING_SYSTEMS(2, "Communal heating systems"),
  LPG_OIL_CENTRAL_HEATING(3, "LPG/oil central heating"),
  WOOD_BURNER_OPEN_FIRE(4, "Wood burner/open fire"),
  BIOMASS_BOILER(5, "Biomass boiler"),
  GROUND_OR_AIR_SOURCE_HEAT_PUMP(6, "Ground or air source heat pump"),
  OTHER(7, "Other"),
  SOLAR_PANELS(8, "Solar panels");

  private final int code;
  private final String description;

  HeatingType(int code, String description) {
    this.code = code;
    this.description = description;
  }

  public int getCode() {
    return code;
  }

  public String getDescription() {
    return description;
  }

  public static HeatingType fromCode(int code) {
    for (HeatingType heatingType : HeatingType.values()) {
      if (heatingType.getCode() == code) {
        return heatingType;
      }
    }
    throw new IllegalArgumentException("Invalid code for HeatingType: " + code);
  }

  /**
   * RightMove heating type
   * 1 Air Conditioning, 2 Central, 3 Double Glazing, 4 Eco-Friendly, 5 Electric, 6 Gas,
   * 7 Gas Central, 8 Night Storage, 9 Oil, 10 Solar, 11 Solar Water, 12 Under Floor
   */
  public static Set<Integer> convertToRightMoveHeatingType(String codes) {
    if (StringUtils.isBlank(codes)) {
      return null;
    }
    Set<Integer> result = new HashSet<>();
    Arrays.stream(codes.split(",")).forEach(i -> {
      int code = Integer.parseInt(i);
      if(HeatingType.ELECTRIC_CENTRAL.code == code) {
        // Electric, Central, Night Storage
        result.add(5);
        result.add(8);
        result.add(2);
      } else if(HeatingType.ROOM_HEATING.code == code) {
        // Air Conditioning, Under Floor
        result.add(1);
        result.add(12);
      } else if(HeatingType.COMMUNAL_HEATING_SYSTEMS.code == code) {
        // Gas Central, Central
        result.add(7);
        result.add(2);
      } else if(HeatingType.LPG_OIL_CENTRAL_HEATING.code == code) {
        // Oil
        result.add(9);
      } else if(HeatingType.WOOD_BURNER_OPEN_FIRE.code == code) {
        // Central
        result.add(2);
      } else if(HeatingType.BIOMASS_BOILER.code == code) {
        // Eco-Friendly
        result.add(4);
      } else if(HeatingType.SOLAR_PANELS.code == code) {
        // Eco-Friendly, Solar, Solar Water
        result.add(10);
        result.add(11);
        result.add(4);
      } else if(HeatingType.GROUND_OR_AIR_SOURCE_HEAT_PUMP.code == code) {
        // Eco-Friendly
        result.add(4);
      }
    });
    return result;
  }
}
