package com.homethy.enums;

import java.util.Objects;

public enum RentalFrequencyType {
  MONTHLY(0, "Monthly"),
  WEEKLY(1, "Weekly"),
  QUARTERLY(2, "Quarterly"),
  BIANNUAL(3, "Biannual"),
  ANNUAL(4, "Annual");

  private final int code;
  private final String description;

  RentalFrequencyType(int code, String description) {
    this.code = code;
    this.description = description;
  }

  public int getCode() {
    return code;
  }

  public String getDescription() {
    return description;
  }

  public static RentalFrequencyType fromCode(int code) {
    for (RentalFrequencyType status : RentalFrequencyType.values()) {
      if (status.getCode() == code) {
        return status;
      }
    }
    throw new IllegalArgumentException("Invalid code for RentalFrequencyType: " + code);
  }

  /**
   * RightMove rent frequency
   * 1 Yearly, 4 Quarterly, 12 Monthly, 52 Weekly, 365 Daily
   */
  public static Integer convertToRightMoveRentFrequency(int code) {
    return switch (code) {
      case 0 -> 12; // 12 Monthly
      case 1 -> 52; // 52 Weekly
      case 2,3 -> 4; // 4 Quarterly
      case 4 -> 1; // 1 Yearly
      default -> 365; //365 Daily
    };
  }

  public static String convertToZooplaType(RentalFrequencyType type) {
    Objects.requireNonNull(type, "RentalFrequency must not be null");
    return switch (type) {
      case WEEKLY-> "per_week";
      case MONTHLY -> "per_month";
      case QUARTERLY, BIANNUAL -> "per_quarter";
      case ANNUAL -> "per_year";
    };
  }

  public static String convertToZooplaType(int code) {
    return convertToZooplaType(fromCode(code));
  }
}
