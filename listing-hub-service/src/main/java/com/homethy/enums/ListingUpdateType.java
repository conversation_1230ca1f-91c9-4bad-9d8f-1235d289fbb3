package com.homethy.enums;

public enum ListingUpdateType {
  ELECTRIC_CENTRAL(1, "New Listing"),
  ROOM_HEATING(2, "Price Change"),
  COMMUNAL_HEATING_SYSTEMS(3, "Status Change");

  private final int code;
  private final String description;

  ListingUpdateType(int code, String description) {
    this.code = code;
    this.description = description;
  }

  public int getCode() {
    return code;
  }

  public String getDescription() {
    return description;
  }

  public static ListingUpdateType fromCode(int code) {
    for (ListingUpdateType type : ListingUpdateType.values()) {
      if (type.getCode() == code) {
        return type;
      }
    }
    throw new IllegalArgumentException("Invalid code for ListingUpdateType: " + code);
  }
}
