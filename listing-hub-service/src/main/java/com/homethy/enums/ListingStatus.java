package com.homethy.enums;

import java.util.Objects;

public enum ListingStatus {
  COMING_SOON(0, "Coming Soon"),
  AVAILABLE(1, "Available"),
  UNDER_OFFER(2, "Under offer"),
  SSTC(3, "SSTC"),
  SOLD(4, "Sold"),
  WITHDRAWN(5, "Withdrawn"),
  LET_AGREED(6, "Let Agreed"),
  LET(7, "Let");

  private final int code;
  private final String description;

  ListingStatus(int code, String description) {
    this.code = code;
    this.description = description;
  }

  public int getCode() {
    return code;
  }

  public String getDescription() {
    return description;
  }

  public static ListingStatus fromCode(int code) {
    for (ListingStatus status : ListingStatus.values()) {
      if (status.getCode() == code) {
        return status;
      }
    }
    throw new IllegalArgumentException("Invalid code for ListingStatus: " + code);
  }

  public static ListingStatus fromPurchaseType(int code, int purchaseType) {
    boolean checkListingStatus = false;
    if (PurchaseType.FOR_SALE.getCode() == purchaseType) {
      checkListingStatus = ListingStatus.COMING_SOON.getCode() == code
          || ListingStatus.AVAILABLE.getCode() == code
          || ListingStatus.UNDER_OFFER.getCode() == code
          || ListingStatus.SSTC.getCode() == code
          || ListingStatus.SOLD.getCode() == code
          || ListingStatus.WITHDRAWN.getCode() == code;
    }
    if (PurchaseType.TO_LET.getCode() == purchaseType) {
      checkListingStatus = ListingStatus.COMING_SOON.getCode() == code
          || ListingStatus.AVAILABLE.getCode() == code
          || ListingStatus.LET_AGREED.getCode() == code
          || ListingStatus.LET.getCode() == code
          || ListingStatus.WITHDRAWN.getCode() == code;
    }
    if (checkListingStatus) {
      return fromCode(code);
    }
    throw new IllegalArgumentException("Invalid code for ListingStatus: " + code);
  }

  public static Integer isActive(int code) {
    boolean isActive = ListingStatus.COMING_SOON.getCode() == code
        || ListingStatus.AVAILABLE.getCode() == code
        || ListingStatus.UNDER_OFFER.getCode() == code
        || ListingStatus.SSTC.getCode() == code
        || ListingStatus.LET_AGREED.getCode() == code;
    return isActive ? TrueOrFalse.TRUE.getCode() : TrueOrFalse.FALSE.getCode();
  }

  /**
   * RightMove reason
   * 7:Sold by Us, 8:Sold by Another Agent, 9:Withdrawn from Market,
   * 10:Lost Instruction, 11:Removed, 12 Let by Us
   */
  public static Integer convertToRightMoveReason(int code) {
    if (ListingStatus.SOLD.code == code) {
      return 7;
    } else if (ListingStatus.WITHDRAWN.code == code) {
      return 9;
    } else if (ListingStatus.LET.code == code) {
      return 12;
    }
    return null;
  }

  /**
   * RightMove status
   * 1 Available, 2 SSTC (sales only), 3 SSTCM (Scottish sales only),
   * 4 Under Offer (sales only), 5 Reserved (sales only), 6 Let Agreed (lettings only)
   */
  public static Integer convertToRightMoveStatus(int code) {
    if (ListingStatus.COMING_SOON.code == code || ListingStatus.AVAILABLE.code == code) {
      return 1;
    } else if (ListingStatus.UNDER_OFFER.code == code) {
      return 4;
    } else if (ListingStatus.SSTC.code == code) {
      return 2;
    } else if (ListingStatus.LET_AGREED.code == code) {
      return 6;
    }
    return 1;
  }

  public static String convertToZooplaStatus(ListingStatus status) {
    Objects.requireNonNull(status, "ListingStatus must not be null");
    return switch (status) {
      case COMING_SOON, AVAILABLE-> "available";
      case UNDER_OFFER -> "under_offer";
      case SSTC -> "sold_subject_to_contract";
      case SOLD -> "sold";
      case LET_AGREED -> "let_agreed";
      case LET -> "let";
      default -> throw new IllegalArgumentException("Invalid ListingStatus");
    };
  }

  public static String convertToZooplaStatus(int code) {
    ListingStatus status = fromCode(code);
    return convertToZooplaStatus(status);
  }
}
