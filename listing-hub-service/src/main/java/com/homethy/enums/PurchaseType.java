package com.homethy.enums;

public enum PurchaseType {
  FOR_SALE(0, "For Sale"),
  TO_LET(1, "To Let");

  private final int code;
  private final String description;

  PurchaseType(int code, String description) {
    this.code = code;
    this.description = description;
  }

  public int getCode() {
    return code;
  }

  public String getDescription() {
    return description;
  }

  public static PurchaseType fromCode(int code) {
    for (PurchaseType status : PurchaseType.values()) {
      if (status.getCode() == code) {
        return status;
      }
    }
    throw new IllegalArgumentException("Invalid code for PurchaseType: " + code);
  }
}
