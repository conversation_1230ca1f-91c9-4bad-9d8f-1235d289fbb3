package com.homethy.enums;

import java.util.EnumSet;

public enum PropertyType {
  TERRACED(0, "Terraced"),
  END_OF_TERRACE(1, "End of Terrace"),
  SEMI_DETACHED(2, "Semi-Detached"),
  DETACHED(3, "Detached"),
  MEWS_HOUSE(4, "Mews house"),
  FLAT(5, "Flat"),
  MAISONETTE(6, "Maisonette"),
  BUNGALOW(7, "Bungalow"),
  TOWN_HOUSE(8, "Town house"),
  COTTAGE(9, "Cottage"),
  FARM_BARN(10, "Farm/Barn"),
  MOBILE_STATIC(11, "Mobile/static"),
  LAND(12, "Land"),
  STUDIO(13, "Studio"),
  BLOCK_OF_FLATS(14, "Block of flats"),
  OFFICE(15, "Office"),
  OTHER(16, "Other"),
  <PERSON>ND<PERSON>(17, "Condo"),
  APARTMENT(18, "Apartment"),
  PENTHOUSE(19, "Penthouse");

  private final int code;
  private final String description;

  PropertyType(int code, String description) {
    this.code = code;
    this.description = description;
  }

  public int getCode() {
    return code;
  }

  public String getDescription() {
    return description;
  }

  public static PropertyType fromCode(int code) {
    for (PropertyType type : PropertyType.values()) {
      if (type.getCode() == code) {
        return type;
      }
    }
    throw new IllegalArgumentException("Invalid code for PropertyType: " + code);
  }

  public static Integer convertToRightMoveType(int code) {
    return switch (code) {
      case 0 -> 1; // 1 Terraced House
      case 1 -> 2; // 2 End of terrace house
      case 2 -> 3; // 3 Semi-detached house
      case 3 -> 4; // 4 Detached house
      case 4 -> 5; // 5 Mews house
      case 5 -> 8; // 8 Flat
      case 6 -> 11; // 11 Maisonette
      case 7 -> 12; // 12 Bungalow
      case 8 -> 22; // 22 Town house
      case 9 -> 23; // 23 Cottage
      case 10 -> 52; // 52 Farm House
      case 11 -> 16; // 16 Mobile home
      case 12 -> 20; // 20 Land (Residential)
      case 13 -> 9; // 9 Studio flat
      case 14 -> 143; // 143 Block of Apartments
      case 15 -> 178; // 178 Office
      case 17, 18 -> 28; // 28 Apartment
      case 19 -> 29; // 29 Penthouse
      default -> 0; // 0 Not Specified
    };
  }

  public static PropertyType rightMoveToPropertyType(int code) {
    return switch (code) {
      case 1, 13 -> TERRACED;
      case 2 -> END_OF_TERRACE;
      case 3, 14, 128 -> SEMI_DETACHED;
      case 4, 15, 21, 131 -> DETACHED;
      case 5 -> MEWS_HOUSE;
      case 7, 8, 28, 29, 44, 56, 59 -> FLAT;
      case 9 -> STUDIO;
      case 10, 11 -> MAISONETTE;
      case 12 -> BUNGALOW;
      case 16, 50, 117 -> MOBILE_STATIC;
      case 20, 107, 110, 241 -> LAND;
      case 22 -> TOWN_HOUSE;
      case 23 -> COTTAGE;
      case 43, 52, 68, 259 -> FARM_BARN;
      case 143 -> BLOCK_OF_FLATS;
      case 178, 184 -> OFFICE;
      default -> OTHER;
    };
  }

  public static String convertToZooplaType(PropertyType type) {
    return switch (type) {
      case TERRACED -> "terraced";
      case END_OF_TERRACE -> "end_terrace";
      case SEMI_DETACHED -> "semi_detached";
      case DETACHED -> "detached";
      case MEWS_HOUSE -> "mews";
      case FLAT, APARTMENT, CONDO -> "flat";
      case MAISONETTE -> "maisonette";
      case BUNGALOW -> "bungalow";
      case TOWN_HOUSE -> "town_house";
      case COTTAGE -> "cottage";
      case FARM_BARN -> "barn_conversion";
      case MOBILE_STATIC -> "park_home";
      case LAND -> "land";
      case STUDIO -> "studio";
      case BLOCK_OF_FLATS -> "block_of_flats";
      case OFFICE -> "office";
      default -> "other";
    };
  }

  public static String convertToZooplaType(int code) {
    PropertyType type = fromCode(code);
    return convertToZooplaType(type);
  }

  public static boolean isZooplaCommercial(PropertyType type) {
    EnumSet<PropertyType> commercialSet = EnumSet.of(
        PropertyType.BLOCK_OF_FLATS,
        PropertyType.OFFICE,
        PropertyType.LAND,
        PropertyType.OTHER);
    return commercialSet.contains(type);
  }

  public static boolean isZooplaCommercial(int code) {
    PropertyType propertyType = fromCode(code);
    return isZooplaCommercial(propertyType);
  }
}
