package com.homethy.enums;

/**
 * <AUTHOR>
 * @date 2024-10-16
 */
public enum ShowingInstructionType {

  ALL_DAY_REQUIRED(0, "24 hour notice required"),
  DOORMAN_WITH_KEY(1, "The keys are with the doorman"),
  COMMERCIAL_RESIDENTIAL(2, "Commercial and Residential"),
  LIMITED_HOURS(3, "Showing hours are limited"),
  FLEXIBLE_HOURS(4, "Showing hours are flexible");

  private final int code;
  private final String description;

  ShowingInstructionType(int code, String description) {
    this.code = code;
    this.description = description;
  }

  public int getCode() {
    return code;
  }

  public String getDescription() {
    return description;
  }

  public static ShowingInstructionType fromCode(int code) {
    for (ShowingInstructionType status : ShowingInstructionType.values()) {
      if (status.getCode() == code) {
        return status;
      }
    }
    throw new IllegalArgumentException("Invalid code for ShowingInstructionType: " + code);
  }
}
