package com.homethy.enums;

/**
 * <AUTHOR>
 * @date 2024/11/18 10:09
 */
public enum IntegrationFailType {
  RIGHT_MOVE_PUBLISH_FAILED(0, "RightMove publish failed"),
  RIGHT_MOVE_WITHDRAW_FAILED(1, "<PERSON><PERSON><PERSON> withdraw failed"),
  ZOOPLA_PUBLISH_FAILED(2, "Zoopla publish failed"),
  ZOOPLA_WITHDRAW_FAILED(3, "<PERSON><PERSON><PERSON> withdraw failed");

  private final int code;
  private final String description;

  IntegrationFailType(int code, String description) {
    this.code = code;
    this.description = description;
  }

  public int getCode() {
    return code;
  }

  public String getDescription() {
    return description;
  }

  public static IntegrationFailType fromCode(int code) {
    for (IntegrationFailType type : IntegrationFailType.values()) {
      if (type.getCode() == code) {
        return type;
      }
    }
    throw new IllegalArgumentException("Invalid code for IntegrationFailType: " + code);
  }
}
