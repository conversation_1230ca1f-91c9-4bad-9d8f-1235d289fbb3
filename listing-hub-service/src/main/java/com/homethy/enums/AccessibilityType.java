package com.homethy.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

public enum AccessibilityType {
  WHEELCHAIR_ACCESS(0, "Wheelchair access"),
  LIFT_ACCESS(1, "Lift access"),
  RAMPED_ACCESS(2, "Ramped access"),
  WIDE_DOORWAYS(3, "Wide doorways"),
  STEP_FREE_ACCESS(4, "Step free access");

  private final int code;
  private final String description;

  AccessibilityType(int code, String description) {
    this.code = code;
    this.description = description;
  }

  public int getCode() {
    return code;
  }

  public String getDescription() {
    return description;
  }

  public static AccessibilityType fromCode(int code) {
    for (AccessibilityType accessibilityType : AccessibilityType.values()) {
      if (accessibilityType.getCode() == code) {
        return accessibilityType;
      }
    }
    throw new IllegalArgumentException("Invalid code for AccessibilityType: " + code);
  }

  public static String convertToZooplaType(int code) {
    return convertToZooplaType(fromCode(code));
  }

  public static String convertToZooplaType(AccessibilityType type) {
    return switch (type) {
      case WHEELCHAIR_ACCESS -> "wheelchair_accessible";
      case LIFT_ACCESS -> "lift_access";
      case RAMPED_ACCESS -> "ramped_access";
      case WIDE_DOORWAYS -> "wide_doorways";
      case STEP_FREE_ACCESS -> "step_free_access";
    };
  }

  /**
   * RightMove Accessibility Type
   * 42 Not suitable for wheelchair users, 37 Level access, 38 Lift access,
   * 39 Ramped access, 40 Wet room, 41 Wide doorways
   */
  public static Set<Integer> convertToRightMoveAccessibilityType(String codes) {
    if (StringUtils.isBlank(codes)) {
      return null;
    }
    Set<Integer> result = new HashSet<>();
    Arrays.stream(codes.split(",")).forEach(i -> {
      int code = Integer.parseInt(i);
      if (AccessibilityType.WHEELCHAIR_ACCESS.code == code || AccessibilityType.RAMPED_ACCESS.code == code) {
        result.add(39);
      } else if (AccessibilityType.LIFT_ACCESS.code == code) {
        result.add(38);
      } else if (AccessibilityType.WIDE_DOORWAYS.code == code) {
        result.add(41);
      } else if (AccessibilityType.STEP_FREE_ACCESS.code == code) {
        result.add(37);
      }
    });
    return result;
  }
}
