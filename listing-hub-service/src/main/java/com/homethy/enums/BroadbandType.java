package com.homethy.enums;

public enum BroadbandType {
  ADSL_COPPER_WIRE(0, "ADSL copper wire"),
  CABLE(1, "Cable"),
  FTTC(2, "FTTC"),
  FTTP(3, "FTTP"),
  OTHER(4, "Other");

  private final int code;
  private final String description;

  BroadbandType(int code, String description) {
    this.code = code;
    this.description = description;
  }

  public int getCode() {
    return code;
  }

  public String getDescription() {
    return description;
  }

  public static BroadbandType fromCode(int code) {
    for (BroadbandType broadbandType : BroadbandType.values()) {
      if (broadbandType.getCode() == code) {
        return broadbandType;
      }
    }
    throw new IllegalArgumentException("Invalid code for BroadbandType: " + code);
  }

  public static String convertToZooplaType(int code) {
    return convertToZooplaType(fromCode(code));
  }

  public static String convertToZooplaType(BroadbandType type) {
    return switch (type) {
      case ADSL_COPPER_WIRE -> "adsl";
      case CABLE -> "cable";
      case FTTC -> "fttc";
      case FTTP -> "fttp";
      case OTHER -> "other";
    };
  }
}
