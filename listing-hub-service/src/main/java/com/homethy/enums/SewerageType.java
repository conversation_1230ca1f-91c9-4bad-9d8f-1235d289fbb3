package com.homethy.enums;

public enum SewerageType {
  MAINLINE_SUPPLY(0, "Mains Supply"),
  SEPTIC_TANK(1, "Septic tank"),
  DOMESTIC_SMALL_TREATMENT_PLANTS(2, "Domestic/small sewage treatment plants"),
  CESSPIT(3, "Cesspit"),
  CESSPOOL(4, "Cesspool"),
  OTHER(5, "Other");

  private final int code;
  private final String description;

  SewerageType(int code, String description) {
    this.code = code;
    this.description = description;
  }

  public int getCode() {
    return code;
  }

  public String getDescription() {
    return description;
  }

  public static SewerageType fromCode(int code) {
    for (SewerageType sewerage : SewerageType.values()) {
      if (sewerage.getCode() == code) {
        return sewerage;
      }
    }
    throw new IllegalArgumentException("Invalid code for SewerageType: " + code);
  }
}
