package com.homethy.enums;

public enum PropertyConstructionType {
  BRICK_AND_BLOCK(0, "Brick and block"),
  TIMBER_FRAME(1, "Timber frame"),
  CONCRETE_BLOCKS(2, "Concrete blocks"),
  STEEL(3, "Steel"),
  INSULATED_CONCRETE_FRAMEWORK(4, "Insulated concrete framework"),
  REINFORCED_CONCRETE(5, "Reinforced concrete"),
  OTHER(6, "Other");

  private final int code;
  private final String description;

  PropertyConstructionType(int code, String description) {
    this.code = code;
    this.description = description;
  }

  public int getCode() {
    return code;
  }

  public String getDescription() {
    return description;
  }

  public static PropertyConstructionType fromCode(int code) {
    for (PropertyConstructionType construction : PropertyConstructionType.values()) {
      if (construction.getCode() == code) {
        return construction;
      }
    }
    throw new IllegalArgumentException("Invalid code for PropertyConstructionType: " + code);
  }
}
