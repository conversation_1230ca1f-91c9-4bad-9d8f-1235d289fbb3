package com.homethy.service;

import com.homethy.persistence.service.base.RedisService;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;

import org.springframework.data.redis.core.StringRedisTemplate;

/**
 * <AUTHOR>
 * @date 2024/11/1 11:24
 */
@Slf4j
@Service
public class ListingHubRedisService {
  public static void setRedisName() {
    System.setProperty("configcenter.redis.name", "alert-redis");
  }

  @Autowired
  RedisService redisService;

  @Autowired
  private StringRedisTemplate stringRedisTemplate;

  private static final String LISTING_HUB = "listingHub";

  private static final String NAME_SPACE = "listingHub.schedule";

  public String get(String key) {
    return redisService.get(LISTING_HUB, key);
  }

  public <T> T get(String key, Class<T> clazz) {
    return redisService.get(LISTING_HUB, key, clazz);
  }

  public Long remove(String key) {
    return redisService.del(LISTING_HUB, key);
  }

  public boolean exists(String key) {
    try {
      return redisService.exists(LISTING_HUB, key);
    } catch (Exception e) {
      log.warn("Redis Error", e);
      return false;
    }
  }

  public void set(String key, Object value, int seconds) {
    try {
      if (value instanceof String) {
        redisService.set(LISTING_HUB, key, (String) value, seconds);
      } else {
        redisService.set(LISTING_HUB, key, value, seconds);
      }
    } catch (Exception e) {
      log.warn("Set redis failed", e);
    }
  }

  public boolean lockSchedule(String schedule) {
    return lockSchedule(schedule, 60 * 5);
  }

  public boolean lockSchedule(String schedule, int expireSeconds) {
    return lock(schedule, expireSeconds);
  }

  public boolean unLockSchedule(String schedule) {
    return unlock(schedule);
  }

  public boolean tryLock(@NonNull String lockDataId) {
    String key = NAME_SPACE + lockDataId;
    return StringUtils.isBlank(redisService.get(LISTING_HUB, key));
  }

  public final boolean lock(@NonNull String lockDataId, int expireSeconds) {
    String key = NAME_SPACE + lockDataId;
    if (redisService.setnx(LISTING_HUB, key, "1") == 1L) {
      redisService.expire(LISTING_HUB, key, expireSeconds);
      return true;
    }
    long ttl = redisService.ttl(LISTING_HUB, key);
    if (ttl <= 0) {
      redisService.expire(LISTING_HUB, key, expireSeconds);
    }
    return false;
  }

  /**
   * Release the lock
   */
  public final boolean unlock(String lockDataId) {
    return redisService.del(LISTING_HUB, NAME_SPACE + lockDataId) == 1L;
  }

  public Long incrBy(String key, int integer) {
    return redisService.incrBy(LISTING_HUB, key, integer);
  }

  public Long expire(String key, int seconds) {
    Long columns = 0L;
    try {
      columns = redisService.expire(LISTING_HUB, key, seconds);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
    }
    return columns;
  }

  public Long hIncrBy(String key, String filed, int integer) {
    return redisService.hincrBy(LISTING_HUB, key, filed, integer);
  }

  public Long lpush(String key, String... strings) {
    return redisService.lpush(LISTING_HUB, key, strings);
  }

  public List<String> lrange(String key, long start, long stop) {
    String newKey = LISTING_HUB.trim() + key.trim();
    List<String> result = null;
    try {
      result = stringRedisTemplate.opsForList().range("001" + newKey, start, stop);
    } catch (Exception e) {
      log.error("redis lrange error:{}", e.getMessage());
    }
    return result;
  }

  public void ltrim(String key, long start, long stop) {
    String newKey = LISTING_HUB.trim() + key.trim();
    try {
      stringRedisTemplate.opsForList().trim("001" + newKey, start, stop);
    } catch (Exception e) {
      log.error("redis ltrim error:{}", e.getMessage());
    }
  }

  public void sadd(String key, String value, int ttlSecond) {
    redisService.sadd(LISTING_HUB, key, ttlSecond, value);
  }

  public boolean sismeber(String key, String member) {
    return redisService.sismember(LISTING_HUB, key, member);
  }
}
