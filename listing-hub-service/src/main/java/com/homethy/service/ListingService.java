package com.homethy.service;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.homethy.microservice.client.bo.p.User;
import com.homethy.model.ListingInfo;
import com.homethy.model.ListingIntegrationInfo;
import com.homethy.model.ListingMultiFieldsUK;
import com.homethy.model.ListingUpdateInfo;
import com.homethy.model.dto.CheckRepeatPropertyResult;
import com.homethy.model.dto.ListingIntegrationInfoReq;
import com.homethy.model.dto.ListingIntegrationInfoResp;
import com.homethy.model.dto.ListingViewsResp;
import com.homethy.model.dto.PostPropertyResult;
import com.homethy.model.dto.SearchCondition;
import com.homethy.model.dto.ThirdPartyListingUpdateReq;

import java.util.List;
import java.util.Map;

public interface ListingService {
  PostPropertyResult postProperty(Map<String, Object> params);

  void deleteProperty(User user, long id, int marketStatus);

  CheckRepeatPropertyResult checkRepeatProperty(Map<String, Object> params);

  List<ListingUpdateInfo> getListingHistory(long listingId);

  ListingIntegrationInfo postIntegrationInfo(ListingIntegrationInfoReq req);

  List<ListingIntegrationInfoResp> listIntegrationInfo(long teamId, long agentId, long officeId);

  ListingIntegrationInfoResp deleteIntegrationInfo(long id, User user);

  String getBranchId(long teamId, long officeId, int integrationType);

  void constructFeatureList(ListingMultiFieldsUK multiFields, ObjectNode node,
                            ListingInfo info, boolean isZoopla);

  List<Integer> updateThirdPartyInfo(ThirdPartyListingUpdateReq req, User user);

  String searchProperty(SearchCondition searchCondition);

  ListingViewsResp getListingViews(long id, int marketStatus, User user);
}
