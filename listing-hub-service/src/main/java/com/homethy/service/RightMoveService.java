package com.homethy.service;

import com.homethy.model.ListingInfo;
import com.homethy.model.dto.RightmoveApplicantDto;

import org.springframework.scheduling.annotation.Async;

import java.util.concurrent.Future;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/11/14 14:31
 */
public interface RightMoveService {
  @Async("asyncExecutor")
  Future<String> sendToRightMove(ListingInfo info);

  @Async("asyncExecutor")
  Future<Boolean> removeFromRightMove(ListingInfo info, ListingInfo oldInfo);

  RightmoveApplicantDto getRightMoveBranchEmails(Integer branchId, Integer rangeMinutes);

  Map<String, Integer> getPropertyDetailViews(ListingInfo info, Date startDate, Date endDate);

}
