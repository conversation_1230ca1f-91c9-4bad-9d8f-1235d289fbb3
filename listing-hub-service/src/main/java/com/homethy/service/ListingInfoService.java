package com.homethy.service;

import com.homethy.model.ListingInfo;
import com.homethy.model.ListingMultiFieldsUK;
import com.homethy.model.ListingUpdateInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @create 2024-10-09 14:49
 */
public interface ListingInfoService {
  ListingInfo createOrUpdateListingInfo(ListingInfo listingInfo, ListingInfo oldListingInfo);

  ListingInfo getListingInfoById(long id);

  ListingInfo findByIdAndMarketStatus(long id, int marketStatus);

  List<ListingUpdateInfo> getListingUpdateInfos(long listingId);

  ListingMultiFieldsUK getListingMultiFields(ListingInfo info);
}
