package com.homethy.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.homethy.enums.AccessibilityType;
import com.homethy.enums.FurnishedType;
import com.homethy.enums.HeatingType;
import com.homethy.enums.IntegrationType;
import com.homethy.enums.ListingStatus;
import com.homethy.enums.ParkingType;
import com.homethy.enums.PropertyType;
import com.homethy.enums.PurchaseType;
import com.homethy.enums.RentFrequencyType;
import com.homethy.enums.RentalFrequencyType;
import com.homethy.enums.TenureType;
import com.homethy.enums.TrueOrFalse;
import com.homethy.model.ListingInfo;
import com.homethy.model.ListingMultiFieldsUK;
import com.homethy.model.ListingSaleInfo;
import com.homethy.model.dto.RightmoveApplicantDto;
import com.homethy.service.ListingInfoService;
import com.homethy.service.ListingService;
import com.homethy.service.RightMoveService;
import com.homethy.util.CommonUtil;
import com.homethy.util.JsonUtil;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.FileInputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.security.KeyStore;
import java.time.LocalDate;
import java.time.Period;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;

import javax.net.ssl.SSLContext;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;

/**
 * <AUTHOR>
 * @date 2024/11/14 14:31
 */
@Service
public class RightMoveServiceImpl implements RightMoveService {

  private static final Logger logger = LoggerFactory.getLogger(RightMoveServiceImpl.class);

  //private static final Integer NETWORK_ID = 13580;

  //private static final Integer BRANCH_ID = 281087;

  @Autowired
  private ListingService listingService;

  @Autowired
  private ListingInfoService listingInfoService;

  // "ZGz1fk8UcM"
  @Value("${RIGHT_MOVE_PASSWORD}")
  private String password;

  // "certificate/rightmove/loftyuktest.jks"
  @Value("${RIGHT_MOVE_FILE_PATH:certificate/rightmove/loftyuktest.jks}")
  private String filePath;

  // 13580
  @Value("${RIGHT_MOVE_NETWORK_ID:13580}")
  private Integer networkId;

  // "https://adfapi.adftest.rightmove.com/v1"
  @Value("${RIGHT_MOVE_DOMAIN:https://adfapi.adftest.rightmove.com/v1}")
  private String domain;

  @Override
  public RightmoveApplicantDto getRightMoveBranchEmails(Integer branchId, Integer rangeMinutes) {
    try {
      ObjectNode network = JsonUtil.createObjectNode();
      network.put("network_id", networkId);

      ObjectNode branch = JsonUtil.createObjectNode();
      branch.put("branch_id", branchId);

      ObjectNode exportPeriod = JsonUtil.createObjectNode();
      DateTimeFormatter formatter = DateTimeFormatter.ofPattern(CommonUtil.UK_DATETIME_PATTERN);
      ZonedDateTime endTime = ZonedDateTime.now(ZoneOffset.UTC);
      String formatEndTime = endTime.format(formatter);
      exportPeriod.put("end_date_time", formatEndTime);
      ZonedDateTime startTime = endTime.minusMinutes(rangeMinutes);
      String formatStartTime = startTime.format(formatter);
      exportPeriod.put("start_date_time", formatStartTime);

      ObjectNode requestBody = JsonUtil.createObjectNode();
      requestBody.set("network", network);
      requestBody.set("branch", branch);
      requestBody.set("export_period", exportPeriod);

      String url = domain + "/property/getbranchemails";
      String json = this.callRightMove(url, JsonUtil.toJson(requestBody));
      if (json == null) {
        return null;
      }
      return JsonUtil.toBean(json, RightmoveApplicantDto.class);
    } catch (Exception e) {
      logger.info("getRightMoveBranchEmails branchId:{}", branchId);
      return null;
    }
  }

  /**
   * publish RightMove
   */
  @Override
  public Future<String> sendToRightMove(ListingInfo info) {
    CompletableFuture<String> cf = new CompletableFuture<>();
    long teamId = Long.parseLong(info.getMlsOrgId());
    long officeId = Long.parseLong(info.getAgentOrganizationId());
    String branchId = listingService.getBranchId(teamId, officeId,
        IntegrationType.RIGHT_MOVE.getCode());
    if (StringUtils.isEmpty(branchId)) {
      logger.error("Send property to RightMove failed, branchId is null");
      cf.complete(null);
      return cf;
    }
    ListingMultiFieldsUK multiFields = JsonUtil.toBean(info.getMultiFields(),
        ListingMultiFieldsUK.class);
    // network
    ObjectNode network = JsonUtil.createObjectNode();
    network.put("network_id", networkId);
    // branch
    ObjectNode branch = JsonUtil.createObjectNode();
    branch.put("branch_id", Integer.parseInt(branchId));
    // 1:Sales, 2:Lettings
    int channel = PurchaseType.FOR_SALE.getCode() == info.getPurchaseType() ? 1 : 2;
    branch.put("channel", channel);
    branch.put("overseas", false);
    // request body
    ObjectNode requestBody = JsonUtil.createObjectNode();
    requestBody.set("network", network);
    requestBody.set("branch", branch);
    requestBody.set("property", this.buildRightMoveProperty(info, multiFields));

    String url = domain + "/property/sendpropertydetails";
    JsonNode resp = JsonUtil.readTree(this.callRightMove(url, JsonUtil.toJson(requestBody)));
    if (resp == null) {
      logger.error("Send property to RightMove failed, resp is null");
      cf.complete(null);
      return cf;
    }
    boolean success = resp.path("success").asBoolean();
    if (success) {
      String rightMoveUrl = resp.path("property").path("rightmove_url").asText();
      cf.complete(rightMoveUrl);
    } else {
      logger.error("Send property to RightMove failed.");
      cf.complete(null);
    }
    return cf;
  }

  private ObjectNode buildRightMoveProperty(ListingInfo info, ListingMultiFieldsUK multiFields) {
    ObjectNode property = JsonUtil.createObjectNode();
    property.put("agent_ref", info.getId());
    property.put("published", true);
    property.put("property_type", PropertyType.convertToRightMoveType(info.getPropertyType()));
    property.put("status", ListingStatus.convertToRightMoveStatus(info.getListingStatus()));
    boolean newHome = Integer.valueOf(TrueOrFalse.TRUE.getCode()).equals(info.getNewBuilt());
    property.put("new_home", newHome);
    String createTime = DateUtil.format(info.getCreateTime(), "dd-MM-yyyy HH:mm:ss");
    property.put("create_date", createTime);
    String now = DateUtil.format(new Date(), "dd-MM-yyyy HH:mm:ss");
    property.put("update_date", now);
    if (multiFields.getSaleInfo() != null && multiFields.getSaleInfo().getAvailableFrom() != null) {
      property.put("date_available",
          DateUtil.format(multiFields.getSaleInfo().getAvailableFrom(), "dd-MM-yyyy"));
    }
    if (PurchaseType.TO_LET.getCode() == info.getPurchaseType()) {
      // 1 Long term, 2 Short term, 4 Commercial, 0 Not specified
      property.put("let_type", 0);
    }
    property.set("address", this.buildRightMoveAddress(info));
    property.set("price_information", this.buildRightMovePriceInfo(info, multiFields));
    property.set("details", this.buildRightMoveDetails(info, multiFields));
    property.set("media", this.buildRightMoveMedia(info));
    return property;
  }

  private ObjectNode buildRightMoveAddress(ListingInfo info) {
    ObjectNode address = JsonUtil.createObjectNode();
    address.put("house_name_number", info.getStreetAddress1());
    address.put("address_2", info.getStreetAddress2());
    address.put("town", StringUtils.isBlank(info.getCity()) ? "N/A" : info.getCity());
    String[] postcodes = info.getZipCode().split(" ");
    address.put("postcode_1", postcodes[0]);
    address.put("postcode_2", postcodes[1]);
    // Address 1, town, County，postcode
    StringBuilder displayAddress = new StringBuilder();
    displayAddress.append(info.getStreetAddress1());
    if (StringUtils.isNotBlank(info.getCity())) {
      displayAddress.append(", ");
      displayAddress.append(info.getCity());
    }
    if (StringUtils.isNotBlank(info.getCountry())) {
      displayAddress.append(", ");
      displayAddress.append(info.getCountry());
    }
    displayAddress.append(", ");
    displayAddress.append(info.getZipCode());
    address.put("display_address", displayAddress.toString());
    address.put("latitude", info.getLatitude());
    address.put("longitude", info.getLongitude());
    address.put("pov_latitude", info.getLatitude());
    address.put("pov_longitude", info.getLongitude());
    return address;
  }

  private ObjectNode buildRightMovePriceInfo(ListingInfo info, ListingMultiFieldsUK multiFields) {
    ObjectNode priceInfo = JsonUtil.createObjectNode();
    ListingSaleInfo saleInfo = multiFields.getSaleInfo();
    if (saleInfo == null) {
      logger.info("buildRightMovePriceInfo: saleInfo is null, id={}", info.getId());
      return priceInfo;
    }
    BigDecimal price = info.getPrice();
    priceInfo.put("price", price);
    // 0 Default
    priceInfo.put("price_qualifier", 0);
    if (saleInfo.getDeposit() != null) {
      priceInfo.put("deposit", saleInfo.getDeposit().intValue());
    }
    if (saleInfo.getRentalFrequency() != null) {
      int rentalFrequency = saleInfo.getRentalFrequency();
      priceInfo.put("rent_frequency",
          RentalFrequencyType.convertToRightMoveRentFrequency(rentalFrequency));

      if (RentalFrequencyType.BIANNUAL.getCode() == rentalFrequency && price != null) {
        price = price.divide(new BigDecimal("2"), 2, RoundingMode.HALF_UP);
        priceInfo.put("price", price);
      }
    }
    priceInfo.put("shared_ownership", false);
    if (saleInfo.getTenure() != null) {
      priceInfo.put("tenure_type", TenureType.convertToRightMoveTenureType(saleInfo.getTenure()));

      if (saleInfo.getTenure().equals(TenureType.LEASEHOLD_SHARE_OF_FREEHOLD.getCode())) {
        priceInfo.put("shared_ownership", true);
      }
    }
    if (saleInfo.getTenureExpiryDate() != null) {
      LocalDate tenureExpiryDate = saleInfo.getTenureExpiryDate().toInstant()
          .atZone(ZoneId.systemDefault()).toLocalDate();

      LocalDate currentDate = LocalDate.now();
      Period period = Period.between(currentDate, tenureExpiryDate);
      priceInfo.put("tenure_unexpired_years", period.getYears());
    }
    if (saleInfo.getOwnershipShared() != null) {
      priceInfo.put("shared_ownership_percentage", saleInfo.getOwnershipShared().doubleValue());
    }
    if (saleInfo.getOwnershipRent() != null) {
      priceInfo.put("shared_ownership_rent", saleInfo.getOwnershipRent().doubleValue());
    }
    if (saleInfo.getRentFrequency() != null) {
      priceInfo.put("shared_ownership_rent_frequency",
          RentFrequencyType.convertToRightMoveShardOwnRentFrequency(saleInfo.getRentFrequency()));
    }
    /*if (saleInfo.getAnnualGroundRent() != null) {
      priceInfo.put("annual_ground_rent", saleInfo.getAnnualGroundRent().doubleValue());
    }
    if (saleInfo.getGroundRentReviewFrequency() != null) {
      priceInfo.put("ground_rent_review_period_years",
          saleInfo.getGroundRentReviewFrequency().doubleValue());
    }
    if (saleInfo.getGroundRentIncrease() != null) {
      priceInfo.put("ground_rent_percentage_increase",
          saleInfo.getGroundRentIncrease().doubleValue());
    }*/
    if (saleInfo.getAnnualServiceCharge() != null) {
      priceInfo.put("annual_service_charge", saleInfo.getAnnualServiceCharge().doubleValue());
    }
    return priceInfo;
  }

  private ObjectNode buildRightMoveDetails(ListingInfo info, ListingMultiFieldsUK multiFields) {
    ObjectNode details = JsonUtil.createObjectNode();
    details.put("summary", info.getDetailsDescribe());
    details.put("description", info.getDetailsDescribe());
    Integer bedrooms = info.parseBedrooms();
    details.put("bedrooms", bedrooms == null ? 0 : bedrooms);
    Integer bathrooms = info.parseBathrooms();
    if (bathrooms != null) {
      details.put("bathrooms", bathrooms);
    }
    Integer receptionRooms = info.parseReceptionRooms();
    if (receptionRooms != null) {
      details.put("reception_rooms", receptionRooms);
    }
    // features
    this.listingService.constructFeatureList(multiFields, details, info, false);
    if (multiFields.getUtilityInfo() != null && multiFields.getUtilityInfo().getParking() != null) {
      Set<Integer> parkingTypes = ParkingType.convertToRightMoveParkingType(
          multiFields.getUtilityInfo().getParking());
      if (CollectionUtil.isNotEmpty(parkingTypes)) {
        ArrayNode arrayNode = details.putArray("parking");
        parkingTypes.forEach(arrayNode::add);
      }
    }
    details.put("year_built", info.getBuiltYear());
    if (multiFields.getGeneralInfo() != null && multiFields.getGeneralInfo().getEntranceFloor() != null) {
      details.put("floors", multiFields.getGeneralInfo().getEntranceFloor().intValue());
    }
    if (multiFields.getUtilityInfo() != null && multiFields.getUtilityInfo().getAccessibility() != null) {
      Set<Integer> accTypes = AccessibilityType.convertToRightMoveAccessibilityType(
          multiFields.getUtilityInfo().getAccessibility());
      if (CollectionUtil.isNotEmpty(accTypes)) {
        ArrayNode arrayNode = details.putArray("accessibility");
        accTypes.forEach(arrayNode::add);
      }
    }
    if (multiFields.getUtilityInfo() != null && multiFields.getUtilityInfo().getHeating() != null) {
      Set<Integer> heatingTypes = HeatingType.convertToRightMoveHeatingType(
          multiFields.getUtilityInfo().getHeating());
      if (CollectionUtils.isNotEmpty(heatingTypes)) {
        ArrayNode arrayNode = details.putArray("heating");
        heatingTypes.forEach(arrayNode::add);
      }
    }
    if (multiFields.getGeneralInfo() != null && multiFields.getGeneralInfo().getFurnished() != null) {
      details.put("furnished_type",
          FurnishedType.convertToRightMoveFurnishedType(multiFields.getGeneralInfo().getFurnished()));
    }
    if (multiFields.getGeneralInfo() != null && multiFields.getGeneralInfo().getPetAllowed() != null) {
      details.put("pets_allowed", Integer.valueOf(TrueOrFalse.TRUE.getCode())
          .equals(multiFields.getGeneralInfo().getPetAllowed()));
    }
    return details;
  }

  private ArrayNode buildRightMoveMedia(ListingInfo info) {
    ArrayNode media = JsonUtil.createArrayNode();
    if (!StringUtils.isBlank(info.getListingPicturesWithType())
        || !StringUtils.isBlank(info.getVideo()) || !StringUtils.isBlank(info.getVirtualTour())) {
      int counter = 0;
      Map<String, Integer> mediaTypeMap = Map.of("picture", 1, "floorplan", 2, "epc", 6);
      if (!StringUtils.isBlank(info.getListingPicturesWithType())) {
        JsonNode pictures = JsonUtil.readTree(info.getListingPicturesWithType());
        if (pictures != null && pictures.isArray()) {
          ArrayNode pictureArr = (ArrayNode) pictures;
          for (JsonNode picture : pictureArr) {
            ObjectNode node = JsonUtil.createObjectNode();
            // 1 Image, 2 Floorplan, 3 Brochure, 4 Virtual Tour, 5 Audio Tour, 6 EPC, 7 EPC Graph
            String mediaType = picture.path("type").asText("picture");
            node.put("media_type", mediaTypeMap.getOrDefault(mediaType, 1));
            node.put("media_url", picture.path("url").asText());
            node.put("sort_order", ++counter);
            media.add(node);
          }
        }
      }
      if (!StringUtils.isBlank(info.getVideo())) {
        ObjectNode node = JsonUtil.createObjectNode();
        // 1 Image, 2 Floorplan, 3 Brochure, 4 Virtual Tour, 5 Audio Tour, 6 EPC, 7 EPC Graph
        node.put("media_type", 4);
        node.put("media_url", info.getVideo());
        node.put("sort_order", ++counter);
        media.add(node);
      }
      if (!StringUtils.isBlank(info.getVirtualTour())) {
        ObjectNode node = JsonUtil.createObjectNode();
        // 1 Image, 2 Floorplan, 3 Brochure, 4 Virtual Tour, 5 Audio Tour, 6 EPC, 7 EPC Graph
        node.put("media_type", 4);
        node.put("media_url", info.getVirtualTour());
        node.put("sort_order", ++counter);
        media.add(node);
      }
    }
    return media;
  }

  @Override
  public Future<Boolean> removeFromRightMove(ListingInfo info, ListingInfo oldInfo) {
    CompletableFuture<Boolean> cf = new CompletableFuture<>();
    if (oldInfo == null || StringUtils.isBlank(oldInfo.getMultiFields())) {
      cf.complete(true);
      return cf;
    }

    // 检查rightMovePublishDate是否为空，为空不需要remove
    ListingMultiFieldsUK oldMultiFields = JsonUtil.toBean(oldInfo.getMultiFields(),
        ListingMultiFieldsUK.class);
    if (oldMultiFields.getThirdPartyInfo() == null
        || oldMultiFields.getThirdPartyInfo().getRightMovePublishDate() == null) {
      cf.complete(true);
      return cf;
    }

    long teamId = Long.parseLong(oldInfo.getMlsOrgId());
    long officeId = Long.parseLong(oldInfo.getAgentOrganizationId());
    String branchId = listingService.getBranchId(teamId, officeId,
        IntegrationType.RIGHT_MOVE.getCode());
    if (StringUtils.isEmpty(branchId)) {
      logger.error("Remove property from RightMove failed, branchId is null");
      cf.complete(false);
      return cf;
    }

    ObjectNode network = JsonUtil.createObjectNode();
    network.put("network_id", networkId);
    ObjectNode branch = JsonUtil.createObjectNode();
    branch.put("branch_id", Integer.parseInt(branchId));
    // 1:Sales, 2:Lettings
    int channel = PurchaseType.FOR_SALE.getCode() == info.getPurchaseType() ? 1 : 2;
    branch.put("channel", channel);
    ObjectNode property = JsonUtil.createObjectNode();
    property.put("agent_ref", info.getId());
    Integer reason = ListingStatus.convertToRightMoveReason(info.getListingStatus());
    property.put("removal_reason", reason);
    String now = DateUtil.format(new Date(), "dd-MM-yyyy HH:mm:ss");
    property.put("transaction_date", now);
    ObjectNode requestBody = JsonUtil.createObjectNode();
    requestBody.set("network", network);
    requestBody.set("branch", branch);
    requestBody.set("property", property);

    String url = domain + "/property/removeproperty";
    JsonNode resp = JsonUtil.readTree(this.callRightMove(url, JsonUtil.toJson(requestBody)));
    if (resp == null) {
      logger.error("Remove property from RightMove failed, resp is null");
      cf.complete(false);
      return cf;
    }
    cf.complete(resp.path("success").asBoolean());
    return cf;
  }

  private String callRightMove(String url, String body) {
    try {
      InputStream inputStream = new FileInputStream(filePath);
      // 加载KeyStore
      char[] password = this.password.toCharArray();
      KeyStore keyStore = KeyStore.getInstance("JKS");
      keyStore.load(inputStream, password);

      // 创建SSLContext
      SSLContext sslContext = SSLContextBuilder.create()
          .loadKeyMaterial(keyStore, password).build();

      try (CloseableHttpClient httpClient = HttpClients.custom().setSSLContext(sslContext)
          .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE).build()) {
        HttpPost httpPost = new HttpPost(url);
        StringEntity entity = new StringEntity(body, ContentType.APPLICATION_JSON);
        httpPost.setEntity(entity);
        HttpResponse response = httpClient.execute(httpPost);
        HttpEntity responseEntity = response.getEntity();
        String resp = EntityUtils.toString(responseEntity);
        logger.info("callRightMove body={}", body);
        logger.info("callRightMove resp={}", resp);
        return resp;
      }
    } catch (Exception e) {
      logger.error("callRightMove error:", e);
      return null;
    }
  }

  @Override
  public Map<String, Integer> getPropertyDetailViews(ListingInfo info, Date startDate,
                                                     Date endDate) {
    if (info == null || StringUtils.isBlank(info.getMultiFields())) {
      return Collections.emptyMap();
    }

    ListingMultiFieldsUK multiFields = JsonUtil.toBean(info.getMultiFields(),
        ListingMultiFieldsUK.class);
    if (multiFields.getThirdPartyInfo() == null
        || multiFields.getThirdPartyInfo().getRightMovePublishDate() == null) {
      return Collections.emptyMap();
    }

    long teamId = Long.parseLong(info.getMlsOrgId());
    long officeId = Long.parseLong(info.getAgentOrganizationId());
    String branchId = listingService.getBranchId(teamId, officeId,
        IntegrationType.RIGHT_MOVE.getCode());
    if (StringUtils.isEmpty(branchId)) {
      logger.error("GetPropertyDetailViews failed, branchId is null");
      return Collections.emptyMap();
    }

    ObjectNode network = JsonUtil.createObjectNode();
    network.put("network_id", networkId);
    ObjectNode branch = JsonUtil.createObjectNode();
    branch.put("branch_id", Integer.parseInt(branchId));
    ObjectNode property = JsonUtil.createObjectNode();
    property.put("agent_ref", info.getId());

    ObjectNode exportPeriod = JsonUtil.createObjectNode();
    String startDateStr = DateUtil.format(startDate, "dd-MM-yyyy");
    exportPeriod.put("start_date", startDateStr);
    String endDateStr = DateUtil.format(endDate, "dd-MM-yyyy");
    exportPeriod.put("end_date", endDateStr);

    ObjectNode requestBody = JsonUtil.createObjectNode();
    requestBody.set("network", network);
    requestBody.set("branch", branch);
    requestBody.set("property", property);
    requestBody.set("export_period", exportPeriod);

    String url = domain + "/property/getpropertyperformance";
    JsonNode resp = JsonUtil.readTree(this.callRightMove(url, JsonUtil.toJson(requestBody)));
    if (resp == null) {
      logger.error("GetPropertyDetailViews failed, resp is null");
      return Collections.emptyMap();
    }

    boolean success = resp.path("success").asBoolean();
    if (success) {
      Map<String, Integer> result = new HashMap<>();
      JsonNode propertyViewsDaily = resp.path("property_views_daily");
      if (propertyViewsDaily.isArray() && !propertyViewsDaily.isEmpty()) {
        for (JsonNode obj : propertyViewsDaily) {
          String date = obj.path("date").asText();
          int totalDetailViews = obj.path("detail_views").path("total_detail_views").asInt();
          result.put(date, totalDetailViews);
        }
      }
      return result;
    } else {
      logger.error("GetPropertyDetailViews failed.");
      return Collections.emptyMap();
    }
  }
}
