package com.homethy.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.homethy.client.IndexClient;
import com.homethy.client.ListingmgmtClient;
import com.homethy.client.SiteTrackerClient;
import com.homethy.dao.listing.ListingInfoDao;
import com.homethy.dao.listing.ListingIntegrationInfoDao;
import com.homethy.enums.AccessibilityType;
import com.homethy.enums.BroadbandType;
import com.homethy.enums.ElectricitySupplyType;
import com.homethy.enums.FurnishedType;
import com.homethy.enums.HeatingType;
import com.homethy.enums.IntegrationFailType;
import com.homethy.enums.IntegrationType;
import com.homethy.enums.ListingDisplayType;
import com.homethy.enums.ListingStatus;
import com.homethy.enums.MarketStatus;
import com.homethy.enums.MobileSignalType;
import com.homethy.enums.ParkingType;
import com.homethy.enums.PropertyConstructionType;
import com.homethy.enums.PropertyType;
import com.homethy.enums.PurchaseType;
import com.homethy.enums.RentFrequencyType;
import com.homethy.enums.RentalFrequencyType;
import com.homethy.enums.SewerageType;
import com.homethy.enums.ShowingInstructionType;
import com.homethy.enums.SourcesOfFloodingType;
import com.homethy.enums.TenureType;
import com.homethy.enums.TrueOrFalse;
import com.homethy.enums.WaterSupplyType;
import com.homethy.manager.UserManager;
import com.homethy.microservice.client.agent.model.AgentQueryBo;
import com.homethy.microservice.client.agent.model.SearchResultBo;
import com.homethy.microservice.client.agent.search.AgentSearchClient;
import com.homethy.microservice.client.bo.p.User;
import com.homethy.model.ListingAccessInfo;
import com.homethy.model.ListingChimeFields;
import com.homethy.model.ListingGeneralInfo;
import com.homethy.model.ListingInfo;
import com.homethy.model.ListingIntegrationInfo;
import com.homethy.model.ListingMultiFieldsUK;
import com.homethy.model.ListingRightAndRestriction;
import com.homethy.model.ListingRiskInfo;
import com.homethy.model.ListingSaleInfo;
import com.homethy.model.ListingSoldHistory;
import com.homethy.model.ListingThirdPartyInfo;
import com.homethy.model.ListingUpdateInfo;
import com.homethy.model.ListingUtilityInfo;
import com.homethy.model.dto.CheckRepeatPropertyDto;
import com.homethy.model.dto.CheckRepeatPropertyResult;
import com.homethy.model.dto.DailyListingViewDto;
import com.homethy.model.dto.ListingIntegrationInfoReq;
import com.homethy.model.dto.ListingIntegrationInfoResp;
import com.homethy.model.dto.ListingViewsResp;
import com.homethy.model.dto.PlatformView;
import com.homethy.model.dto.PostPropertyResult;
import com.homethy.model.dto.SearchCondition;
import com.homethy.model.dto.ThirdPartyListingUpdateReq;
import com.homethy.service.ListingInfoService;
import com.homethy.service.ListingService;
import com.homethy.service.RightMoveService;
import com.homethy.service.ZooplaService;
import com.homethy.util.JsonUtil;
import com.homethy.util.SynonymReplacer;
import com.lofty.site.client.SiteClient;
import com.lofty.site.model.base.SiteInfo;

import org.apache.commons.beanutils.BeanUtilsBean;
import org.apache.commons.beanutils.converters.BigDecimalConverter;
import org.apache.commons.beanutils.converters.DateConverter;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;

import static com.homethy.enums.IntegrationType.RIGHT_MOVE;

/**
 * <AUTHOR>
 * @date 2024-09-25
 */
@Service
public class ListingServiceImpl implements ListingService {

  private static final Logger logger = LoggerFactory.getLogger(ListingServiceImpl.class);

  private static final String SELLING_INDEX = "listing_uk";

  private static final String SOLD_INDEX = "sold_listing";

  private static final String EXPIRED_INDEX = "expired_listing";

  @Autowired
  private ListingInfoService listingInfoService;

  @Autowired
  private RightMoveService rightMoveService;

  @Autowired
  private ZooplaService zooplaService;

  @Autowired
  private IndexClient indexClient;

  @Autowired
  private ListingmgmtClient listingmgmtClient;

  @Autowired
  private AgentSearchClient agentSearchClient;

  @Autowired
  private ListingInfoDao listingInfoDao;

  @Autowired
  private ListingIntegrationInfoDao integrationInfoDao;

  @Autowired
  private UserManager userManager;

  @Autowired
  private SiteTrackerClient siteTrackerClient;

  @Autowired
  private SiteClient siteClient;


  private static final List<String> FIELD_NAMES = Collections.synchronizedList(new ArrayList<>());

  static {
    Class<?>[] classes = {
        ListingInfo.class, ListingGeneralInfo.class, ListingSaleInfo.class,
        ListingUtilityInfo.class, ListingRightAndRestriction.class, ListingRiskInfo.class,
        ListingAccessInfo.class, ListingThirdPartyInfo.class, ListingChimeFields.class
    };
    for (Class<?> clazz : classes) {
      Field[] fields = clazz.getDeclaredFields();
      for (Field field : fields) {
        FIELD_NAMES.add(field.getName());
      }
      FIELD_NAMES.remove("multiFields");
      FIELD_NAMES.add("listingPictures");
      FIELD_NAMES.add("pricePerSqm");
      FIELD_NAMES.add("pricePerSqft");
      FIELD_NAMES.add("baths");
      FIELD_NAMES.add("previewPicture");
      FIELD_NAMES.add("daysOnList");
      FIELD_NAMES.add("lastPrimaryChangeTime");
      FIELD_NAMES.add("primaryAgentId");
      FIELD_NAMES.add("agentId1");
      FIELD_NAMES.add("agentId2");
      FIELD_NAMES.add("agentId3");
      FIELD_NAMES.add("listDate");
      FIELD_NAMES.add("detailLink");
    }
  }

  @Override
  public CheckRepeatPropertyResult checkRepeatProperty(Map<String, Object> params) {
    long userId = Long.parseLong(params.get("currentUserId").toString());
    params.put("agentOrganizationId", this.userManager.getUserOfficeId(userId));

    List<ListingInfo> listingInfos = this.getRepeatProperty(params);
    CheckRepeatPropertyResult result;
    if (CollectionUtil.isEmpty(listingInfos)) {
      result = CheckRepeatPropertyResult.builder().repeatFlag(false)
          .properties(new ArrayList<>()).build();
    } else {
      List<CheckRepeatPropertyDto> properties = listingInfos.stream().map(info -> {
        CheckRepeatPropertyDto dto = new CheckRepeatPropertyDto();
        BeanUtils.copyProperties(info, dto);
        ListingMultiFieldsUK multiFields = JsonUtil.toBean(info.getMultiFields(),
            ListingMultiFieldsUK.class);
        dto.setAskPrice(info.getPrice());
        dto.setRightMovePublishDate(multiFields.getThirdPartyInfo().getRightMovePublishDate());
        dto.setZooplaPublishDate(multiFields.getThirdPartyInfo().getZooplaPublishDate());
        dto.setSoldHistories(multiFields.getSoldHistories());
        return dto;
      }).collect(Collectors.toList());
      result = CheckRepeatPropertyResult.builder().repeatFlag(true).properties(properties).build();
    }
    return result;
  }

  private List<ListingInfo> getRepeatProperty(Map<String, Object> params) {
    // this.checkMandatoryPropertyFields(params);
    String mlsOrgId = params.get("mlsOrgId").toString();
    String agentOrganizationId = params.get("agentOrganizationId").toString();
    String key = this.generateDuplicateKey(params);
    List<ListingInfo> infos =
        this.listingInfoDao.findRepeatListingInfo(mlsOrgId, key, agentOrganizationId);
    if (params.get("id") != null) {
      long listingId = Long.parseLong(params.get("id").toString());
      infos = infos.stream().filter(info -> info.getId() != listingId).collect(Collectors.toList());
    }
    return infos;
  }

  @Override
  public PostPropertyResult postProperty(Map<String, Object> params) {
    // 重复房源 更新DB，切换ES index（调用两次），删除rightmove和zoopla
    // 新增房源 添加DB，添加ES（调用一次），上传rightmove和zoopla
    // 更新房源 更新DB，更新ES（调用一次）或者切换ES index（调用两次），更新/删除rightmove和zoopla

    // 重复房源：1.删除rightmove和zoopla, 2.更新DB, 3.切换ES index（调用两次）
    // 新增房源：写入DB获取ID
    // 新增/修改房源：1.上传/更新/删除rightmove和zoopla, 2.更新DB, 3.添加/更新ES（调用一次）/切换ES index（调用两次）

    // 处理房源字段
    ListingInfo info = this.convertToListing(params);
    // 校验重复房源
    List<ListingInfo> repeatList = this.getRepeatProperty(params);
    boolean noRepeat = CollectionUtil.isEmpty(repeatList) ||
        (repeatList.size() == 1 && repeatList.get(0).getId().equals(info.getId()));
    if (!noRepeat) {
      throw new RuntimeException("Found existed property");
    }
    // 创建房源，插入DB，生成ID
    boolean createFlag = info.getId() == null;
    ListingInfo oldInfo = null;
    if (createFlag) {
      info = listingInfoService.createOrUpdateListingInfo(info, oldInfo);
    } else {
      oldInfo = listingInfoService.getListingInfoById(info.getId());
      this.checkPublishFlag(params, oldInfo);
    }
    PostPropertyResult result = PostPropertyResult.builder()
        .id(info.getId()).build();

    // 处理重复房源
    // this.removeExistedListings(params, repeatList);
    // 处理rightmove zoopla website
    result.setFailTypes(this.processThirdParty(params, info, oldInfo));
    // 更新房源，更新DB
    info = listingInfoService.createOrUpdateListingInfo(info, oldInfo);
    // 处理ES
    String currentUserName = params.get("currentUserName") == null ? "" :
        params.get("currentUserName").toString();
    this.saveES(currentUserName, info, oldInfo);
    return result;
  }

  @Override
  public void deleteProperty(User user, long id, int marketStatus) {
    ListingInfo info = listingInfoService.findByIdAndMarketStatus(id, marketStatus);
    if (info == null) {
      throw new RuntimeException("Listing not existed");
    }
    this.checkListingInfoTeamPermission(info, user);
    Map<String, Object> params = new HashMap<>();
    params.put("publishZoopla", 0);
    params.put("publishRightMove", 0);
    params.put("chimeListingDisplay", "no");
    List<Integer> thirdPartyRes
        = this.processThirdParty(params, info, info);
    if (CollUtil.isNotEmpty(thirdPartyRes)) {
      logger.warn("third party update error:{}", thirdPartyRes);
    }
    info.setDeleteFlag(1);
    info.setUpdateUser(String.valueOf(user.getId()));
    info.setUpdateTime(new Date());
    info = listingInfoService.createOrUpdateListingInfo(info, info);
    String userName = user.getFirstName() + user.getLastName();
    this.saveES(userName, info, info);
  }

  /**
   * 1. upload/update/remove listing from rightmove zoopla website
   * 2. Maintain chimeFields and thirdPartyInfo attributes in multiFields
   */
  private List<Integer> processThirdParty(Map<String, Object> params, ListingInfo info,
                                          ListingInfo oldInfo) {
    ListingMultiFieldsUK multiFields = listingInfoService.getListingMultiFields(info);
    if (info.getDraftFlag() == TrueOrFalse.TRUE.getCode()) {
      // draft listing can't be published
      multiFields.setThirdPartyInfo(new ListingThirdPartyInfo());
      multiFields.setChimeFields(
          new ListingChimeFields(ListingDisplayType.NO_WEBSITE.getCode(), null, null)
      );
      logger.info("property is draft, multiFields:{}", multiFields);
      info.setMultiFields(JsonUtil.toJson(multiFields));
      return Collections.emptyList();
    }
    String needPublish = Integer.toString(TrueOrFalse.TRUE.getCode());
    Future<String> rightmovePublish = null;
    Future<Boolean> rightmoveRemove = null;
    Future<String> zooplaPublish = null;
    Future<Boolean> zooplaRemove = null;
    Date now = new Date();
    List<Integer> errorCodes = new ArrayList<>();
    Set<Integer> removeStatus = Set.of(
        ListingStatus.SOLD.getCode(),
        ListingStatus.LET.getCode(),
        ListingStatus.WITHDRAWN.getCode());
    if (oldInfo != null) {
      ListingMultiFieldsUK oldMultiFields = listingInfoService.getListingMultiFields(oldInfo);
      multiFields.setThirdPartyInfo(oldMultiFields.getThirdPartyInfo());
      multiFields.setChimeFields(oldMultiFields.getChimeFields());
    }
    ListingThirdPartyInfo thirdPartyInfo = multiFields.getThirdPartyInfo();
    if (thirdPartyInfo == null) {
      thirdPartyInfo = new ListingThirdPartyInfo();
    }
    logger.info("thirdParty oldInfo:{}", thirdPartyInfo);
    // rightMove
    if (params.get("publishRightMove") == null) {
      if (thirdPartyInfo.getRightMoveUrl() != null && info != oldInfo) {
        // publish to rightMove before, and listing changed
        if (removeStatus.contains(info.getListingStatus())) {
          rightmoveRemove = rightMoveService.removeFromRightMove(info, oldInfo);
        } else {
          // update rightMove
          rightmovePublish = rightMoveService.sendToRightMove(info);
        }
      }
    } else {
      String rightMoveFlag = params.get("publishRightMove").toString();
      if (needPublish.equals(rightMoveFlag)
          && !removeStatus.contains(info.getListingStatus())) {
        // need publish
        rightmovePublish = rightMoveService.sendToRightMove(info);
      } else {
        rightmoveRemove = rightMoveService.removeFromRightMove(info, oldInfo);
      }
    }

    // zoopla
    if (params.get("publishZoopla") == null) {
      if (thirdPartyInfo.getZooplaUrl() != null && info != oldInfo) {
        // publish to zoopla before, and listing changed
        if (removeStatus.contains(info.getListingStatus())) {
          zooplaRemove = zooplaService.removeFromZoopla(info, oldInfo);
        } else {
          // update zoopla
          zooplaPublish = zooplaService.sendToZoopla(info);
        }
      }
    } else {
      String zooplaFlag = params.get("publishZoopla").toString();
      if (needPublish.equals(zooplaFlag)
          && !removeStatus.contains(info.getListingStatus())) {
        // need publish
        zooplaPublish = zooplaService.sendToZoopla(info);
      } else {
        zooplaRemove = zooplaService.removeFromZoopla(info, oldInfo);
      }
    }

    if (rightmovePublish != null) {
      try {
        String rightmoveUrl = rightmovePublish.get();
        if (rightmoveUrl != null) {
          thirdPartyInfo.setRightMoveUrl(rightmoveUrl);
          thirdPartyInfo.setPublishFlag(TrueOrFalse.TRUE.getCode());
          thirdPartyInfo.setRightMoveLastPublishDate(now);
          if (thirdPartyInfo.getRightMovePublishDate() == null) {
            thirdPartyInfo.setRightMovePublishDate(now);
          }
        } else {
          errorCodes.add(IntegrationFailType.RIGHT_MOVE_PUBLISH_FAILED.getCode());
        }
      } catch (Exception e) {
        logger.error("Publish to rightmove failed", e);
        errorCodes.add(IntegrationFailType.RIGHT_MOVE_PUBLISH_FAILED.getCode());
      }
    }
    if (rightmoveRemove != null) {
      try {
        Boolean flag = rightmoveRemove.get();
        if (Boolean.TRUE.equals(flag)) {
          thirdPartyInfo.setRightMoveUrl(null);
          thirdPartyInfo.setRightMovePublishDate(null);
          thirdPartyInfo.setRightMoveLastPublishDate(null);
        } else {
          errorCodes.add(IntegrationFailType.RIGHT_MOVE_WITHDRAW_FAILED.getCode());
        }
      } catch (Exception e) {
        logger.error("Remove from rightmove failed", e);
        errorCodes.add(IntegrationFailType.RIGHT_MOVE_WITHDRAW_FAILED.getCode());
      }
    }
    if (zooplaPublish != null) {
      try {
        String zooplaUrl = zooplaPublish.get();
        if (zooplaUrl != null) {
          thirdPartyInfo.setZooplaUrl(zooplaUrl);
          thirdPartyInfo.setZooplaLastPublishDate(now);
          thirdPartyInfo.setPublishFlag(TrueOrFalse.TRUE.getCode());
          if (thirdPartyInfo.getZooplaPublishDate() == null) {
            thirdPartyInfo.setZooplaPublishDate(now);
          }
        } else {
          errorCodes.add(IntegrationFailType.ZOOPLA_PUBLISH_FAILED.getCode());
        }
      } catch (Exception e) {
        logger.error("Publish to zoopla failed", e);
        errorCodes.add(IntegrationFailType.ZOOPLA_PUBLISH_FAILED.getCode());
      }
    }
    if (zooplaRemove != null) {
      try {
        Boolean flag = zooplaRemove.get();
        if (Boolean.TRUE.equals(flag)) {
          thirdPartyInfo.setZooplaUrl(null);
          thirdPartyInfo.setZooplaPublishDate(null);
          thirdPartyInfo.setZooplaLastPublishDate(null);
        } else {
          errorCodes.add(IntegrationFailType.ZOOPLA_WITHDRAW_FAILED.getCode());
        }
      } catch (Exception e) {
        logger.error("Remove from zoopla failed", e);
        errorCodes.add(IntegrationFailType.ZOOPLA_WITHDRAW_FAILED.getCode());
      }
    }
    // website
    ListingChimeFields oldChiemFields = multiFields.getChimeFields();
    if (oldChiemFields == null) {
      oldChiemFields = new ListingChimeFields();
    }
    if (info.getListingStatus() != null &&
        ListingStatus.WITHDRAWN == ListingStatus.fromCode(info.getListingStatus())) {
      params.put("chimeListingDisplay", "no");
    }
    this.updateChimeFields(params, oldChiemFields, now, thirdPartyInfo);
    multiFields.setChimeFields(oldChiemFields);
    multiFields.setThirdPartyInfo(thirdPartyInfo);
    logger.info("new chiemFields:{}", oldChiemFields);
    logger.info("new thirdPartyInfo:{}", thirdPartyInfo);
    info.setMultiFields(JsonUtil.toJson(multiFields));
    return errorCodes;
  }

  /**
   * update old ChimeFields
   * @param params         params by FE
   * @param oldChimeFields old ChimeFields
   * @param now            Date
   * @param thirdPartyInfo Updated thirdPartyInfo
   */
  private void updateChimeFields(Map<String, Object> params,
                                 ListingChimeFields oldChimeFields, Date now,
                                 ListingThirdPartyInfo thirdPartyInfo) {
    if (params.get("chimeListingDisplay") != null) {
      String siteDisplay = params.get("chimeListingDisplay").toString();
      oldChimeFields.setChimeListingDisplay(siteDisplay);
      ListingDisplayType displayType = ListingDisplayType.fromCode(siteDisplay);
      switch (displayType) {
        case NO_WEBSITE -> {
          oldChimeFields.setOnMyWebsiteDate(null);
          oldChimeFields.setOnTeamWebsiteDate(null);
        }
        case MY_WEBSITE -> {
          if (oldChimeFields.getOnMyWebsiteDate() == null) {
            oldChimeFields.setOnMyWebsiteDate(now);
            thirdPartyInfo.setPublishFlag(TrueOrFalse.TRUE.getCode());
          }
        }
        case TEAM_WEBSITE -> {
          if (oldChimeFields.getOnTeamWebsiteDate() == null) {
            oldChimeFields.setOnTeamWebsiteDate(now);
            thirdPartyInfo.setPublishFlag(TrueOrFalse.TRUE.getCode());
          }
        }
      }
    }
  }

  private ListingInfo convertToListing(Map<String, Object> params) {
    long userId = Long.parseLong(params.get("currentUserId").toString());
    params.put("agentOrganizationId", this.userManager.getUserOfficeId(userId));

    // check params before convert
    this.checkPropertyFields(params);
    // generate duplicate key
    params.put("duplicateKey1", this.generateDuplicateKey(params));

    ListingGeneralInfo generalInfo = new ListingGeneralInfo();
    ListingRightAndRestriction rightAndRestriction = new ListingRightAndRestriction();
    ListingRiskInfo riskInfo = new ListingRiskInfo();
    ListingSaleInfo saleInfo = new ListingSaleInfo();
    ListingUtilityInfo utilityInfo = new ListingUtilityInfo();
    ListingAccessInfo accessInfo = new ListingAccessInfo();
    ListingThirdPartyInfo thirdPartyInfo = new ListingThirdPartyInfo();
    List<ListingSoldHistory> soldHistories = new ArrayList<>();
    ListingInfo info = new ListingInfo();

    try {
      BeanUtilsBean beanUtilsBean = new BeanUtilsBean();
      beanUtilsBean.getConvertUtils().register(new BigDecimalConverter(null), BigDecimal.class);
      beanUtilsBean.getConvertUtils().register(new DateConverter(null), Date.class);
      beanUtilsBean.populate(generalInfo, params);
      beanUtilsBean.populate(rightAndRestriction, params);
      beanUtilsBean.populate(riskInfo, params);
      beanUtilsBean.populate(saleInfo, params);
      beanUtilsBean.populate(utilityInfo, params);
      beanUtilsBean.populate(accessInfo, params);
      beanUtilsBean.populate(thirdPartyInfo, params);
      if (params.get("soldHistories") != null) {
        soldHistories = (List) params.get("soldHistories");
      }
      beanUtilsBean.populate(info, params);
      ListingMultiFieldsUK multiFields = ListingMultiFieldsUK.builder()
          .generalInfo(generalInfo)
          .rightAndRestriction(rightAndRestriction)
          .riskInfo(riskInfo)
          .saleInfo(saleInfo)
          .utilityInfo(utilityInfo)
          .accessInfo(accessInfo).thirdPartyInfo(thirdPartyInfo)
          .soldHistories(soldHistories).build();
      info.setMultiFields(JsonUtil.toJson(multiFields));
      // soldPrice & soldDate
      Optional.of(soldHistories)
          .filter(CollUtil::isNotEmpty)
          .flatMap(histories -> histories.stream()
              .max(Comparator.comparing(ListingSoldHistory::getSoldDate))
          )
          .ifPresent(newestSoldHistory -> {
            info.setSoldPrice(newestSoldHistory.getSoldPrice());
            info.setSoldDate(newestSoldHistory.getSoldDate());
          });

      if (Long.parseLong(info.getAgentOrganizationId()) != 0L) {
        info.setAgentOrganizationName(this.userManager.getOfficeNameById(Long.parseLong(info.getAgentOrganizationId())));
      } else {
        info.setAgentOrganizationName(this.userManager.getTeamNameById(Long.parseLong(info.getMlsOrgId()),
            Long.parseLong(info.getPrimaryAgentId())));
      }
    } catch (Exception e) {
      logger.error("postProperty:convert fields failed, params={}", params, e);
      throw new RuntimeException("postProperty:convert fields failed");
    }
    return info;
  }

  private void checkPropertyFields(Map<String, Object> params) {
    if (CollectionUtil.isEmpty(params)) {
      throw new RuntimeException("postProperty:params are empty");
    }
    params.put("displayOnInternet", TrueOrFalse.TRUE.getCode());
    try {
      if (params.get("id") == null) {
        params.put("createTime", new Date());
        params.put("createUser", params.get("currentUserId"));
        params.put("createUserName", params.get("currentUserName"));
        params.put("deleteFlag", TrueOrFalse.FALSE.getCode());
        params.put("publishFlag", TrueOrFalse.FALSE.getCode());
      }
      // check mandatory fields
      List<String> nullFields = this.checkMandatoryPropertyFields(params);
      // check enum fields
      this.checkEnumPropertyFields(params);
      // process field values
      this.processPropertyFields(params);

      if (CollectionUtil.isEmpty(nullFields)) {
        params.put("draftFlag", TrueOrFalse.FALSE.getCode());
      } else {
        if (nullFields.contains("publishFlag") ||
            TrueOrFalse.TRUE.getCode() == Integer.parseInt(params.get("publishFlag").toString())) {
          throw new RuntimeException("postProperty:mandatory field is null, " + nullFields);
        } else {
          params.put("draftFlag", TrueOrFalse.TRUE.getCode());
        }
      }
    } catch (Exception e) {
      logger.error("postProperty:check fields failed, params={}", params, e);
      throw e;
    }
  }

  private List<String> checkMandatoryPropertyFields(Map<String, Object> params) {
    List<String> fields = new ArrayList<>();
    if (params.get("streetAddress1") == null
        || StringUtils.isBlank(params.get("streetAddress1").toString())) {
      fields.add("streetAddress1");
    }
    if (params.get("purchaseType") == null) {
      fields.add("purchaseType");
    }
    if (params.get("propertyType") == null) {
      fields.add("propertyType");
    }
    if (params.get("listingStatus") == null) {
      fields.add("listingStatus");
    }
    if (params.get("primaryAgentName") == null
        || StringUtils.isBlank(params.get("primaryAgentName").toString())) {
      fields.add("primaryAgentName");
    }
    if (params.get("primaryAgentId") == null
        || StringUtils.isBlank(params.get("primaryAgentId").toString())) {
      fields.add("primaryAgentId");
    }
    if (params.get("bedrooms") == null || StringUtils.isBlank(params.get("bedrooms").toString())) {
      fields.add("bedrooms");
    }
    if (params.get("bathrooms") == null || StringUtils.isBlank(params.get("bathrooms").toString())) {
      fields.add("bathrooms");
    }
    if (params.get("detailsDescribe") == null
        || StringUtils.isBlank(params.get("detailsDescribe").toString())) {
      fields.add("detailsDescribe");
    }
    if (params.get("showingInstruction") == null) {
      fields.add("showingInstruction");
    }
    if (params.get("showingDate") == null
        || StringUtils.isBlank(params.get("showingDate").toString())) {
      fields.add("showingDate");
    }
    if (params.get("mlsOrgId") == null || StringUtils.isBlank(params.get("mlsOrgId").toString())) {
      fields.add("mlsOrgId");
    }
    if (params.get("publishRightMove") == null) {
      fields.add("publishRightMove");
    }
    if (params.get("publishZoopla") == null) {
      fields.add("publishZoopla");
    }
    if (params.get("chimeListingDisplay") == null
        || StringUtils.isBlank(params.get("chimeListingDisplay").toString())) {
      fields.add("chimeListingDisplay");
    }
    if (params.get("zipCode") == null || StringUtils.isBlank(params.get("zipCode").toString())) {
      fields.add("zipCode");
    }
    if (params.get("listingPicturesWithType") == null
        || StringUtils.isBlank(params.get("listingPicturesWithType").toString())) {
      fields.add("listingPicturesWithType");
    }
    if (String.valueOf(PurchaseType.FOR_SALE.getCode()).equals(params.get("purchaseType").toString())) {
      if (params.get("tenure") == null) {
        fields.add("tenure");
      }
      if (params.get("askPrice") == null || StringUtils.isBlank(params.get("askPrice").toString())) {
        fields.add("askPrice");
        params.put("askPrice", -1);
      }
    }
    if (String.valueOf(PurchaseType.TO_LET.getCode()).equals(params.get("purchaseType").toString())) {
      if (params.get("rentalFrequency") == null) {
        fields.add("rentalFrequency");
      }
      if (params.get("price") == null || StringUtils.isBlank(params.get("price").toString())) {
        fields.add("price");
        params.put("price", -1);
      }
      if (params.get("deposit") == null || StringUtils.isBlank(params.get("deposit").toString())) {
        fields.add("deposit");
      }
    }
    // publish flag
    if (params.get("publishFlag") == null || StringUtils.isBlank(params.get("publishFlag").toString())) {
      fields.add("publishFlag");
    }
    return fields;
  }

  private void checkEnumPropertyFields(Map<String, Object> params) {
    PurchaseType.fromCode(Integer.parseInt(params.get("purchaseType").toString()));
    PropertyType.fromCode(Integer.parseInt(params.get("propertyType").toString()));
    ListingStatus.fromPurchaseType(Integer.parseInt(params.get("listingStatus").toString()),
        Integer.parseInt(params.get("purchaseType").toString()));
    if (params.get("newBuilt") != null) {
      TrueOrFalse.fromCode(Integer.parseInt(params.get("newBuilt").toString()));
    }
    if (params.get("propertyConstruction") != null) {
      PropertyConstructionType.fromCode(Integer.parseInt(params.get("propertyConstruction").toString()));
    }
    if (params.get("furnished") != null) {
      FurnishedType.fromCode(Integer.parseInt(params.get("furnished").toString()));
    }
    if (params.get("petAllowed") != null) {
      TrueOrFalse.fromCode(Integer.parseInt(params.get("petAllowed").toString()));
    }
    if (params.get("tenure") != null) {
      TenureType.fromCode(Integer.parseInt(params.get("tenure").toString()));
    }
    if (params.get("rentFrequency") != null) {
      RentFrequencyType.fromCode(Integer.parseInt(params.get("rentFrequency").toString()));
    }
    if (params.get("rentalFrequency") != null) {
      RentalFrequencyType.fromCode(Integer.parseInt(params.get("rentalFrequency").toString()));
    }
    if (params.get("electricitySupply") != null) {
      List.of(params.get("electricitySupply").toString().split(","))
          .forEach(i -> ElectricitySupplyType.fromCode(Integer.parseInt(i)));
    }
    if (params.get("waterSupply") != null) {
      List.of(params.get("waterSupply").toString().split(","))
          .forEach(i -> WaterSupplyType.fromCode(Integer.parseInt(i)));
    }
    if (params.get("sewerage") != null) {
      List.of(params.get("sewerage").toString().split(","))
          .forEach(i -> SewerageType.fromCode(Integer.parseInt(i)));
    }
    if (params.get("heating") != null) {
      List.of(params.get("heating").toString().split(","))
          .forEach(i -> HeatingType.fromCode(Integer.parseInt(i)));
    }
    if (params.get("broadband") != null) {
      List.of(params.get("broadband").toString().split(","))
          .forEach(i -> BroadbandType.fromCode(Integer.parseInt(i)));
    }
    if (params.get("mobileSignal") != null) {
      List.of(params.get("mobileSignal").toString().split(","))
          .forEach(i -> MobileSignalType.fromCode(Integer.parseInt(i)));
    }
    if (params.get("parking") != null) {
      List.of(params.get("parking").toString().split(","))
          .forEach(i -> ParkingType.fromCode(Integer.parseInt(i)));
    }
    if (params.get("accessibility") != null) {
      List.of(params.get("accessibility").toString().split(","))
          .forEach(i -> AccessibilityType.fromCode(Integer.parseInt(i)));
    }
    if (params.get("conservationArea") != null) {
      TrueOrFalse.fromCode(Integer.parseInt(params.get("conservationArea").toString()));
    }
    if (params.get("leaseRestrictions") != null) {
      TrueOrFalse.fromCode(Integer.parseInt(params.get("leaseRestrictions").toString()));
    }
    if (params.get("listedBuilding") != null) {
      TrueOrFalse.fromCode(Integer.parseInt(params.get("listedBuilding").toString()));
    }
    if (params.get("permittedDevelopment") != null) {
      TrueOrFalse.fromCode(Integer.parseInt(params.get("permittedDevelopment").toString()));
    }
    if (params.get("restrictiveCovenant") != null) {
      TrueOrFalse.fromCode(Integer.parseInt(params.get("restrictiveCovenant").toString()));
    }
    if (params.get("propertySubletting") != null) {
      TrueOrFalse.fromCode(Integer.parseInt(params.get("propertySubletting").toString()));
    }
    if (params.get("treePreservationOrder") != null) {
      TrueOrFalse.fromCode(Integer.parseInt(params.get("treePreservationOrder").toString()));
    }
    if (params.get("floodedInLast5Years") != null) {
      TrueOrFalse.fromCode(Integer.parseInt(params.get("floodedInLast5Years").toString()));
    }
    if (params.get("sourcesOfFlooding") != null) {
      List.of(params.get("sourcesOfFlooding").toString().split(","))
          .forEach(i -> SourcesOfFloodingType.fromCode(Integer.parseInt(i)));
    }
    if (params.get("erosionRisk") != null) {
      TrueOrFalse.fromCode(Integer.parseInt(params.get("erosionRisk").toString()));
    }
    if (params.get("coalfieldImpacted") != null) {
      TrueOrFalse.fromCode(Integer.parseInt(params.get("coalfieldImpacted").toString()));
    }
    if (params.get("openHouseFlag") != null) {
      TrueOrFalse.fromCode(Integer.parseInt(params.get("openHouseFlag").toString()));
    }
    if (params.get("showingInstruction") != null) {
      ShowingInstructionType.fromCode(Integer.parseInt(params.get("showingInstruction").toString()));
    }
    if (params.get("publishRightMove") != null) {
      TrueOrFalse.fromCode(Integer.parseInt(params.get("publishRightMove").toString()));
    }
    if (params.get("publishZoopla") != null) {
      TrueOrFalse.fromCode(Integer.parseInt(params.get("publishZoopla").toString()));
    }
    if (params.get("chimeListingDisplay") != null) {
      ListingDisplayType.fromCode(params.get("chimeListingDisplay").toString());
    }
  }

  private void processPropertyFields(Map<String, Object> params) {
    Date now = new Date();

    int listingStatus = Integer.parseInt(params.get("listingStatus").toString());
    MarketStatus marketStatus = MarketStatus.fromListingStatus(listingStatus);
    params.put("marketStatus", marketStatus.getCode());
    params.put("updateTime", now);
    params.put("updateUser", params.get("currentUserId"));
    params.put("updateUserName", params.get("currentUserName"));
    if (params.get("stories") != null) {
      params.put("stories", new BigDecimal(params.get("stories").toString()));
    }
    if (params.get("entranceFloor") != null) {
      params.put("entranceFloor", new BigDecimal(params.get("entranceFloor").toString()));
    }
    if (params.get("totalBuildingSqm") != null) {
      params.put("totalBuildingSqm", new BigDecimal(params.get("totalBuildingSqm").toString()));
    }
    if (params.get("totalBuildingSqft") != null) {
      params.put("totalBuildingSqft", new BigDecimal(params.get("totalBuildingSqft").toString()));
    }
    if (params.get("annualServiceCharge") != null) {
      params.put("annualServiceCharge",
          new BigDecimal(params.get("annualServiceCharge").toString()));
    }
    if (params.get("annualGroundRent") != null) {
      params.put("annualGroundRent", new BigDecimal(params.get("annualGroundRent").toString()));
    }
    if (params.get("groundRentReviewFrequency") != null) {
      params.put("groundRentReviewFrequency", new BigDecimal(params.get(
          "groundRentReviewFrequency").toString()));
    }
    if (params.get("groundRentIncrease") != null) {
      params.put("groundRentIncrease", new BigDecimal(params.get("groundRentIncrease").toString()));
    }
    if (params.get("askPrice") != null) {
      params.put("price", new BigDecimal(params.get("askPrice").toString()));
    }
    if (params.get("ownershipShared") != null) {
      params.put("ownershipShared", new BigDecimal(params.get("ownershipShared").toString()));
    }
    if (params.get("ownershipRent") != null) {
      params.put("ownershipRent", new BigDecimal(params.get("ownershipRent").toString()));
    }
    if (params.get("commonholdAssessment") != null) {
      params.put("commonholdAssessment",
          new BigDecimal(params.get("commonholdAssessment").toString()));
    }
    if (params.get("price") != null) {
      params.put("price", new BigDecimal(params.get("price").toString()));
    }
    if (params.get("deposit") != null) {
      params.put("deposit", new BigDecimal(params.get("deposit").toString()));
    }
    if (params.get("epcExpiryDate") != null) {
      params.put("epcExpiryDate", DateUtil.parse(params.get("epcExpiryDate").toString(),
          DatePattern.NORM_DATETIME_PATTERN));
    }
    if (params.get("tenureExpiryDate") != null) {
      params.put("tenureExpiryDate", DateUtil.parse(params.get("tenureExpiryDate").toString(),
          DatePattern.NORM_DATETIME_PATTERN));
    }
    if (params.get("serviceChargeReviewDate") != null) {
      params.put("serviceChargeReviewDate",
          DateUtil.parse(params.get("serviceChargeReviewDate").toString(),
              DatePattern.NORM_DATETIME_PATTERN));
    }
    if (params.get("groundReviewDate") != null) {
      params.put("groundReviewDate", DateUtil.parse(params.get("groundReviewDate").toString(),
          DatePattern.NORM_DATETIME_PATTERN));
    }
    if (params.get("lastFloodingDate") != null) {
      params.put("lastFloodingDate", DateUtil.parse(params.get("lastFloodingDate").toString(),
          DatePattern.NORM_DATETIME_PATTERN));
    }
    if (params.get("showingDate") != null) {
      params.put("showingDate", DateUtil.parse(params.get("showingDate").toString(),
          DatePattern.NORM_DATETIME_PATTERN));
    }
    if (params.get("availableFrom") != null) {
      params.put("availableFrom", DateUtil.parse(params.get("availableFrom").toString(),
          DatePattern.NORM_DATETIME_PATTERN));
    }
    if (params.get("availableTo") != null) {
      params.put("availableTo", DateUtil.parse(params.get("availableTo").toString(),
          DatePattern.NORM_DATETIME_PATTERN));
    }
    if (params.get("soldHistories") != null) {
      params.put("soldHistories",
          JsonUtil.toList(params.get("soldHistories").toString(), ListingSoldHistory.class));
    }
  }

  private String generateDuplicateKey(Map<String, Object> params) {
    // address line 1
    String formatAddressLine1 = params.get("streetAddress1").toString().toLowerCase()
        .replaceAll("\\s+", " ");
    formatAddressLine1 = SynonymReplacer.replaceSynonyms(formatAddressLine1);
    // address line 2
    String formatAddressLine2 = "";
    if (params.get("streetAddress2") != null) {
      formatAddressLine2 = params.get("streetAddress2").toString().toLowerCase()
          .replaceAll("\\s+", " ");
      formatAddressLine2 = SynonymReplacer.replaceSynonyms(formatAddressLine2);
    }
    // postcode
    String formatZipCode = params.get("zipCode").toString().toLowerCase()
        .replaceAll("\\s+", " ");
    // purchase type
    String purchaseType = params.get("purchaseType").toString();
    // active listing status
    int isActive = ListingStatus.isActive(Integer.parseInt(params.get("listingStatus").toString()));
    // generate MD5
    String text = formatAddressLine1 + "*" + formatAddressLine2 + "*" + formatZipCode
        + "*" + purchaseType + "*" + isActive;
    logger.info("generate duplicate key: {}", text);
    try {
      return this.generateMD5(text);
    } catch (NoSuchAlgorithmException e) {
      logger.error("generateMD5 error: text is {}", text, e);
      throw new RuntimeException("generate duplicate key failed");
    }
  }

  private String generateMD5(String text) throws NoSuchAlgorithmException {
    MessageDigest md = MessageDigest.getInstance("MD5");
    byte[] digest = md.digest(text.getBytes());
    StringBuilder sb = new StringBuilder();
    for (byte b : digest) {
      sb.append(String.format("%02x", b));
    }
    return sb.toString();
  }

  private void checkPublishFlag(Map<String, Object> params, ListingInfo oldInfo) {
    ListingMultiFieldsUK multiFields = JsonUtil.toBean(oldInfo.getMultiFields(),
        ListingMultiFieldsUK.class);
    boolean publishFlag = TrueOrFalse.TRUE.getCode() == multiFields.getThirdPartyInfo()
        .getPublishFlag();
    boolean samePurchaseType = oldInfo.getPurchaseType().toString()
        .equals(params.get("purchaseType").toString());
    if (publishFlag && !samePurchaseType) {
      throw new RuntimeException("Can not change purchase type after launch property");
    }
  }

  // 添加/更新ES（调用一次）, 切换ES index（调用两次）
  private void saveES(String currentUserName, ListingInfo info, ListingInfo oldInfo) {
    if (oldInfo == null || info.getMarketStatus().equals(oldInfo.getMarketStatus())) {
      // 新增/更新ES（调用一次）
      this.sendES(currentUserName, info);
    } else {
      // 切换ES index（调用两次）
      this.sendES(currentUserName, oldInfo);
      this.sendES(currentUserName, info);
    }
  }

  private void sendES(String currentUserName, ListingInfo info) {
    Map<String, Object> indexRequest = new HashMap<>();
    indexRequest.put("indexType", this.getIndexName(info.getMarketStatus()));
    indexRequest.put("ids", info.getId());
    indexRequest.put("requestUser", currentUserName);
    Map<String, Object> result = indexClient.index(indexRequest);
    try {
      logger.info("postProperty:saveES result={}", JsonUtil.toJson(result));
    } catch (Exception e) {
      logger.info("postProperty:saveES print result fail, indexRequest={}", indexRequest, e);
    }
    if (CollectionUtil.isEmpty(result) || result.get("code") == null
        || !result.get("code").toString().equals("200")) {
      logger.error("postProperty:saveES fail, indexRequest={}", indexRequest);
      throw new RuntimeException("postProperty:saveES failed");
    }
  }

  private String getIndexName(int marketStatus) {
    if (MarketStatus.SELLING.getCode() == marketStatus) {
      return SELLING_INDEX;
    } else if (MarketStatus.SOLD.getCode() == marketStatus) {
      return SOLD_INDEX;
    } else {
      return EXPIRED_INDEX;
    }
  }

  @Override
  public List<ListingUpdateInfo> getListingHistory(long listingId) {
    return listingInfoService.getListingUpdateInfos(listingId);
  }

  /**
   * check permissions from the team dimension
   *
   * @param teamId current user teamId
   * @param info   ListingIntegrationInfo
   */
  private void checkIntegrationInfoPermissionV1(long teamId, ListingIntegrationInfo info) {
    assert info != null;
    if (Long.parseLong(info.getTeamId()) != teamId) {
      throw new RuntimeException("Invalid permission");
    }
  }

  @Override
  public ListingIntegrationInfo postIntegrationInfo(ListingIntegrationInfoReq req) {
    String userId = String.valueOf(req.getAgentId());
    logger.info("postIntegrationInfo: agentId={}, teamId={}, officeId={}",
        userId, req.getTeamId(), req.getOfficeId());
    // generate new multiFields
    IntegrationType type = req.getType();
    String multiFields = "";
    ListingIntegrationInfo integrationInfo = null;
    if (req.getId() == null) {
      // insert
      switch (type) {
        case ZOOPLA, RIGHT_MOVE ->
            multiFields = this.updateIntegrationMultiFields(req, new HashMap<>());
        default -> throw new IllegalArgumentException("Invalid type");
      }
      integrationInfo = ListingIntegrationInfo.builder()
          .integrationType(type.getCode())
          .agentOrganizationId(String.valueOf(req.getOfficeId()))
          .teamId(String.valueOf(req.getTeamId()))
          .createUser(userId).updateUser(userId).multiFields(multiFields).build();
      integrationInfoDao.insert(integrationInfo);
    } else {
      // update
      integrationInfo =
          integrationInfoDao.findById(req.getId());
      this.checkIntegrationInfoPermissionV1(req.getTeamId(), integrationInfo);
      Map<String, Object> map = JsonUtil.toMap(integrationInfo.getMultiFields());
      integrationInfo.setMultiFields(this.updateIntegrationMultiFields(req, map));
      integrationInfo.setUpdateUser(userId);
      integrationInfo.setUpdateTime(new Date());
      integrationInfoDao.updateById(integrationInfo);
    }
    return integrationInfo;
  }

  /**
   * Incrementally updating MultiFields
   */
  private String updateIntegrationMultiFields(ListingIntegrationInfoReq req,
                                              Map<String, Object> map) {
    if (req.getBranchId() != null) {
      map.put("branchId", req.getBranchId());
    }
    if (req.getSourceId() != null) {
      map.put("sourceId", req.getSourceId());
    }
    if (req.getSendWelcomeEmail() != null) {
      map.put("sendWelcomeEmail", req.getSendWelcomeEmail());
    }
    return JsonUtil.toJson(map);
  }

  @Override
  public List<ListingIntegrationInfoResp> listIntegrationInfo(long teamId, long agentId,
                                                              long officeId) {
    List<ListingIntegrationInfo> integrationInfos =
        integrationInfoDao.getByTeamAndOffice(String.valueOf(teamId), String.valueOf(officeId));
    return integrationInfos.stream()
        .filter(i -> i.getIntegrationType() == IntegrationType.ZOOPLA.getCode() ||
            i.getIntegrationType() == IntegrationType.RIGHT_MOVE.getCode())
        .map(i -> ListingIntegrationInfoResp.builder()
            .id(i.getId())
            .type(IntegrationType.fromCode(i.getIntegrationType()))
            .multiFields(JsonUtil.toMap(i.getMultiFields())).build())
        .toList();
  }

  /**
   * remove branchId
   * @param id integration info id
   * @param user current user
   */
  @Override
  public ListingIntegrationInfoResp deleteIntegrationInfo(long id, User user) {
    ListingIntegrationInfo integrationInfo = integrationInfoDao.findById(id);
    if (integrationInfo == null) {
      return null;
    }
    long agentId = user.getId();
    long curOfficeId = userManager.getUserOfficeId(agentId);
    logger.info("deleteIntegrationInfo: agentId={}, curOfficeId={}", agentId, curOfficeId);
    // check permission
    this.checkIntegrationInfoPermissionV1(user.getTeamId(), integrationInfo);
    Map<String, Object> map = JsonUtil.toMap(integrationInfo.getMultiFields());
    map.remove("branchId");
    integrationInfo.setUpdateUser(String.valueOf(agentId));
    integrationInfo.setUpdateTime(new Date());
    integrationInfo.setMultiFields(JsonUtil.toJson(map));
    integrationInfoDao.updateById(integrationInfo);
    return ListingIntegrationInfoResp.builder()
        .id(integrationInfo.getId())
        .type(IntegrationType.fromCode(integrationInfo.getIntegrationType()))
        .multiFields(map)
        .build();
  }

  @Override
  public String getBranchId(long teamId, long officeId, int integrationType) {
    ListingIntegrationInfo info = integrationInfoDao.findBranchIdByTeamAndOffice(
        String.valueOf(teamId), String.valueOf(officeId), integrationType);
    if (info != null) {
      return info.getBranchId();
    }
    return null;
  }


  @Override
  public void constructFeatureList(ListingMultiFieldsUK multiFields, ObjectNode node,
                                   ListingInfo info, boolean isZoopla) {
    String name = isZoopla ? "feature_list" : "features";
    ArrayNode featureList = node.putArray(name);
    if (info.getPropertyType() != null) {
      String propertyTypeStr = isZoopla ?
          PropertyType.convertToZooplaType(info.getPropertyType()) :
          PropertyType.fromCode(info.getPropertyType()).getDescription();
      featureList.add("Property Type: " + propertyTypeStr);
    }
    if (info.getBuiltYear() != null) {
      featureList.add("Year Built: " + info.getBuiltYear());
    }
    if (info.getPropertyConstruction() != null) {
      featureList.add("Property Construction: " +
          PropertyConstructionType.fromCode(info.getPropertyConstruction()).getDescription());
    }
    if (info.getBedrooms() != null) {
      featureList.add("Beds: " + info.getBedrooms());
    }
    if (info.getBathrooms() != null) {
      featureList.add("Baths: " + info.getBathrooms());
    }
    if (info.getReceptionRooms() != null) {
      featureList.add("Receptions: " + info.getReceptionRooms());
    }
    if (multiFields.getGeneralInfo() != null) {
      ListingGeneralInfo generalInfo = multiFields.getGeneralInfo();
      if (generalInfo.getEntranceFloor() != null) {
        featureList.add("Entrance Floor: " + generalInfo.getEntranceFloor().intValue());
      }
      if (StringUtils.isNotEmpty(generalInfo.getEpcRating())) {
        featureList.add("EPC Rating: " + generalInfo.getEpcRating());
      }
      if (generalInfo.getFurnished() != null) {
        String furnishedStr = isZoopla ?
            FurnishedType.convertToZooplaType(generalInfo.getFurnished()) :
            FurnishedType.fromCode(generalInfo.getFurnished()).getDescription();
        featureList.add("Furnished: " + furnishedStr);
      }
      if (generalInfo.getPetAllowed() != null) {
        if (TrueOrFalse.TRUE.getCode() == generalInfo.getPetAllowed()) {
          featureList.add("Pet Allowed: Yes");
        } else {
          featureList.add("Pet Allowed: No");
        }
      }
    }
    if (multiFields.getUtilityInfo() != null && isZoopla) {
      ListingUtilityInfo utilityInfo = multiFields.getUtilityInfo();
      if (StringUtils.isNotEmpty(utilityInfo.getParking())) {
        String parkingStr = Arrays.stream(utilityInfo.getParking().split(","))
            .map(Integer::parseInt)
            .map(ParkingType::convertToZooplaType)
            .flatMap(java.util.Collection::stream)
            .distinct()
            .collect(Collectors.joining(", "));
        featureList.add("Parking: " + parkingStr);
      }
      if (StringUtils.isNotEmpty(utilityInfo.getAccessibility())) {
        String accessibilityStr = Arrays.stream(utilityInfo.getAccessibility().split(","))
            .map(Integer::parseInt)
            .map(AccessibilityType::convertToZooplaType)
            .collect(Collectors.joining(", "));
        featureList.add("Accessibility: " + accessibilityStr);
      }
    }
  }

  /**
   * check permission in team dimensions
   *
   * @param info listingInfo
   * @param user current user
   */
  private void checkListingInfoTeamPermission(ListingInfo info, User user) {
    long teamId = user.getTeamId();
    String mlsOrgId = info.getMlsOrgId();
    if (Long.parseLong(mlsOrgId) != teamId) {
      logger.warn("agentId:{}, teamId:{}", user.getId(), user.getTeamId());
      throw new RuntimeException("Invalid permission");
    }
  }

  @Override
  public List<Integer> updateThirdPartyInfo(ThirdPartyListingUpdateReq req, User user) {
    ListingInfo info =
        listingInfoService.findByIdAndMarketStatus(req.getPropertyId(), req.getMarketStatus());
    if (info == null) {
      throw new RuntimeException("Property not existed");
    }
    if (info.getDraftFlag() == TrueOrFalse.TRUE.getCode()) {
      // draft listing can't be published
      logger.info("property is draft, propertyId:{}", info.getId());
      throw new RuntimeException("property is draft");
    }
    this.checkListingInfoTeamPermission(info, user);

    Map<String, Object> params = new HashMap<>();
    params.put("publishZoopla", req.getPublishZoopla());
    params.put("publishRightMove", req.getPublishRightMove());
    params.put("chimeListingDisplay", req.getChimeListingDisplay());
    List<Integer> res = this.processThirdParty(params, info, info);
    listingInfoService.createOrUpdateListingInfo(info, info);
    String userName = user.getFirstName() + user.getLastName();
    // update data in ES
    this.saveES(userName, info, null);
    return res;
  }

  @Override
  public ListingViewsResp getListingViews(long listingId, int marketStatus, User user) {
    ListingInfo info = listingInfoService.findByIdAndMarketStatus(listingId, marketStatus);
    if (info == null) {
      throw new RuntimeException("Property not existed");
    }
    this.checkListingInfoTeamPermission(info, user);
    ListingViewsResp resp = new ListingViewsResp();
    ArrayList<PlatformView> views = new ArrayList<>();

    long agentId = user.getId();
    // website views
    PlatformView websiteView = this.getWebsiteView(info, agentId);
    views.add(websiteView);
    // rightMove views
    PlatformView rightMoveView = this.getRightMoveView(info);
    views.add(rightMoveView);

    resp.setPlatformView(views);
    resp.setTotal(rightMoveView.getTotal() + websiteView.getTotal());
    return resp;
  }

  private PlatformView getWebsiteView(ListingInfo info, long agentId) {
    assert info != null;
    PlatformView view =
        PlatformView.builder().platformName("WEBSITE").build();
    try {
      SiteInfo siteInfo = siteClient.getRealSiteInfo(agentId);
      if (siteInfo == null) {
        logger.warn("No site found for agentId:{}", agentId);
        return view;
      }
      logger.info("siteId:{}", siteInfo.getId());
      Map<String, Object> requestMap = Map.of(
          "siteId", siteInfo.getId(),
          "listingIdList", Collections.singletonList(info.getId()),
          "day", 365 * 10);

      Map<Long, List<DailyListingViewDto>> dailyListingViews =
          siteTrackerClient.getDailyListingViews(requestMap);
      List<DailyListingViewDto> dailyViews = dailyListingViews.getOrDefault(info.getId(),
          Collections.emptyList());
      if (CollUtil.isNotEmpty(dailyViews)) {
        LocalDate today = LocalDate.now();
        String todayStr =
            today.format(DatePattern.PURE_DATE_FORMATTER);
        view.setToday(this.getSpecifiedTimeViews(dailyViews, todayStr));
        String sevenDaysAgo =
            today.minusDays(7).format(DatePattern.PURE_DATE_FORMATTER);
        view.setLast7Days(this.getSpecifiedTimeViews(dailyViews, sevenDaysAgo));
        String fourteenDaysAgo =
            today.minusDays(14).format(DatePattern.PURE_DATE_FORMATTER);
        view.setLast14Days(this.getSpecifiedTimeViews(dailyViews, fourteenDaysAgo));
        // used for calculate total views, the earliest date will be no earlier than 2025
        String earliestDate = "20200101";
        view.setTotal(this.getSpecifiedTimeViews(dailyViews, earliestDate));
      }
      return view;
    } catch (Exception e) {
      logger.error("get website view error:{}", info.getId(), e);
      return view;
    }
  }


  /**
   * @param dailyViews daily listing views
   * @param endDate    format: yyyyMMdd, 7 days ago or 14 days ago
   * @return views from endDate to today
   */
  private long getSpecifiedTimeViews(List<DailyListingViewDto> dailyViews, String endDate) {
    return dailyViews.stream().filter(d -> d.getStatTime().compareTo(endDate) >= 0)
        .mapToLong(DailyListingViewDto::getPv)
        .sum();
  }

  private PlatformView getRightMoveView(ListingInfo info) {
    assert info != null;
    Date now = new Date();
    Date date28DaysAgo = DateUtil.offsetDay(now, -28);
    PlatformView view =
        PlatformView.builder().platformName(RIGHT_MOVE.name()).build();
    try {
      Map<String, Integer> viewMap =
          rightMoveService.getPropertyDetailViews(info, date28DaysAgo, now);
      logger.info("rightMove views:{}", viewMap);
      DateTime today = DateUtil.date();
      DateTime last7Days = DateUtil.offsetDay(today, -7);
      DateTime last14Days = DateUtil.offsetDay(today, -14);
      DateTime last28Days = DateUtil.offsetDay(today, -28);
      int todayCount = 0;
      int last7DaysCount = 0;
      int last14DaysCount = 0;
      int last28DaysCount = 0;

      for (Map.Entry<String, Integer> entry : viewMap.entrySet()) {
        DateTime entryDate = DateUtil.parse(entry.getKey(), "dd-MM-yyyy");
        int count = entry.getValue();

        if (DateUtil.isSameDay(entryDate, today)) {
          todayCount += count;
        }
        if (entryDate.isAfterOrEquals(last7Days) && entryDate.isBeforeOrEquals(today)) {
          last7DaysCount += count;
        }
        if (entryDate.isAfterOrEquals(last14Days) && entryDate.isBeforeOrEquals(today)) {
          last14DaysCount += count;
        }
        if (entryDate.isAfterOrEquals(last28Days) && entryDate.isBeforeOrEquals(today)) {
          last28DaysCount += count;
        }
      }
      view.setToday(todayCount);
      view.setLast7Days(last7DaysCount);
      view.setLast14Days(last14DaysCount);
      view.setTotal(last28DaysCount);
      logger.info("rightMove view counts - today:{}, last7Days:{}, last14Days:{}, last28Days:{}",
          todayCount, last7DaysCount, last14DaysCount, last28DaysCount);
      return view;
    } catch (Exception e) {
      logger.error("get rightMove views error:{}", info.getId(), e);
      return view;
    }
  }

  @Override
  public String searchProperty(SearchCondition searchCondition) {
    searchCondition.setDisplayFieldNames(FIELD_NAMES);

    //logger.info("searchProperty searchCondition:{}", JsonUtil.toJson(searchCondition));
    String properties = listingmgmtClient.searchListingWithLead(
        searchCondition.getSiteId(), searchCondition);
    //logger.info("searchProperty result:{}", properties);
    return this.addPropertyAgent(properties);
  }

  private String addPropertyAgent(String properties) {
    JsonNode routNode = JsonUtil.readTree(properties);
    if (routNode == null) {
      return properties;
    }
    JsonNode dataNode = routNode.get("data");
    if (dataNode == null) {
      return properties;
    }
    JsonNode resultNode = dataNode.get("searchResult");
    if (resultNode == null || !resultNode.isArray()) {
      return properties;
    }
    ArrayNode propertyArr = (ArrayNode) resultNode;
    List<Long> userIds = new ArrayList<>();
    for (JsonNode property : propertyArr) {
      JsonNode node = property.get("primaryAgentId");
      if (node != null) {
        Long primaryAgentId = Long.parseLong(node.asText());
        if (!userIds.contains(primaryAgentId)) {
          userIds.add(primaryAgentId);
        }
      }
      JsonNode baths = property.get("baths");
      if (baths != null) {
        ObjectNode objectNode = (ObjectNode) property;
        objectNode.put("bathrooms", baths.asText());
      }
    }
    Map<Long, ObjectNode> userMap = new HashMap<>();
    AgentQueryBo agentQueryBo = new AgentQueryBo();
    agentQueryBo.setUserIds(userIds);
    agentQueryBo.setFrom(0);
    agentQueryBo.setSize(1999);
    try {
      SearchResultBo searchResultBo = agentSearchClient.search(agentQueryBo);
      searchResultBo.getAgentEsVoList().forEach(agent -> {
        if (agent != null && !userMap.containsKey(agent.getUserId())) {
          ObjectNode agentNode = JsonUtil.createObjectNode();
          agentNode.put("chimeTeamId", agent.getTeamId());
          agentNode.put("email", agent.getAccount());
          agentNode.put("phone", agent.getPhoneNumber());
          agentNode.put("userHeadUrl", agent.getHeadUrl());
          agentNode.put("userOrAgentId", agent.getUserId());
          agentNode.put("userOrAgentName", agent.getFullName());
          userMap.put(agent.getUserId(), agentNode);
        }
      });
    } catch (Exception e) {
      logger.error("addPropertyAgent: search agent error, agentQueryBo={}",
          JsonUtil.toJson(agentQueryBo), e);
      return properties;
    }
    if (CollectionUtil.isNotEmpty(userMap)) {
      for (JsonNode property : propertyArr) {
        JsonNode node = property.get("primaryAgentId");
        if (node == null) {
          continue;
        }
        Long primaryAgentId = Long.parseLong(node.asText());
        if (userMap.containsKey(primaryAgentId) && property.isObject()) {
          ObjectNode objectNode = (ObjectNode) property;
          objectNode.set("listingUserInfo", userMap.get(primaryAgentId));
        }
      }
    }
    return JsonUtil.toJson(routNode);
  }
}
