package com.homethy.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.homethy.client.zoopla.ZooplaFeignClient;
import com.homethy.dao.listing.ListingIntegrationInfoDao;
import com.homethy.enums.AccessibilityType;
import com.homethy.enums.BroadbandType;
import com.homethy.enums.ElectricitySupplyType;
import com.homethy.enums.FurnishedType;
import com.homethy.enums.IntegrationType;
import com.homethy.enums.ListingStatus;
import com.homethy.enums.ParkingType;
import com.homethy.enums.PropertyConstructionType;
import com.homethy.enums.PropertyType;
import com.homethy.enums.PurchaseType;
import com.homethy.enums.RentalFrequencyType;
import com.homethy.enums.TenureType;
import com.homethy.enums.TrueOrFalse;
import com.homethy.exception.ListingHubException;
import com.homethy.manager.UserManager;
import com.homethy.microservice.client.model.LeadAddRequestBo;
import com.homethy.microservice.client.model.LeadInquiriesBo;
import com.homethy.microservice.client.model.LeadPropertyVoBo;
import com.homethy.microservice.client.model.LocationElemBo;
import com.homethy.microservice.client.model.Pair;
import com.homethy.microservice.client.model.UserPhoneBo;
import com.homethy.model.ListingGeneralInfo;
import com.homethy.model.ListingInfo;
import com.homethy.model.ListingIntegrationInfo;
import com.homethy.model.ListingMultiFieldsUK;
import com.homethy.model.ListingRightAndRestriction;
import com.homethy.model.ListingSaleInfo;
import com.homethy.model.ListingUtilityInfo;
import com.homethy.model.dto.ZooplaApplicantDto;
import com.homethy.model.zoopla.ZooplaDeleteResp;
import com.homethy.model.zoopla.ZooplaPublishResp;
import com.homethy.service.ListingHubLeadService;
import com.homethy.service.ListingInfoService;
import com.homethy.service.ListingService;
import com.homethy.service.ZooplaService;
import com.homethy.util.JsonUtil;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import cn.hutool.core.date.DatePattern;

/**
 * <AUTHOR>
 * @date 2024/10/31 13:26
 */
@Service
public class ZooplaServiceImpl implements ZooplaService {

  private static final Logger logger = LoggerFactory.getLogger(ZooplaServiceImpl.class);

  @Autowired
  private ListingService listingService;

  @Autowired
  private ZooplaFeignClient zooplaFeignClient;

  @Autowired
  private ListingInfoService listingInfoService;

  @Autowired
  private ListingIntegrationInfoDao integrationInfoDao;

  @Autowired
  private ListingHubLeadService listingHubLeadService;

  @Autowired
  private UserManager userManager;

  @Override
  public Future<String> sendToZoopla(ListingInfo info) {
    CompletableFuture<String> cf = new CompletableFuture<>();
    // check zoopla branchId exists
    long teamId = Long.parseLong(info.getMlsOrgId());
    long officeId = Long.parseLong(info.getAgentOrganizationId());
    String branchId = listingService.getBranchId(teamId, officeId,
        IntegrationType.ZOOPLA.getCode());
    if (StringUtils.isEmpty(branchId)) {
      logger.error("zooplaBranchId is empty, can't publish to Zoopla, teamId={}, "
              + "officeId={}, listingId={}",
          teamId, officeId, info.getId());
      cf.complete(null);
      return cf;
    }
    ObjectNode zooplaListing = constructZooplaListing(info, branchId);
    try {
      ZooplaPublishResp response = zooplaFeignClient.postListings(zooplaListing.toString());
      logger.info("zoopla url:{}", response.getUrl());
      cf.complete(response.getUrl());
      return cf;
    } catch (ListingHubException e) {
      logger.error("zooplaListing:{}", zooplaListing.toPrettyString());
      cf.complete(null);
      return cf;
    }
  }

  private ObjectNode constructZooplaListing(ListingInfo listingInfo, String zooplaBranchId) {
    ListingMultiFieldsUK multiFields = listingInfoService.getListingMultiFields(listingInfo);

    int propertyType = listingInfo.getPropertyType();
    boolean isCommercial = PropertyType.isZooplaCommercial(propertyType);
    boolean isRent = (listingInfo.getPurchaseType() == PurchaseType.TO_LET.getCode());

    ObjectNode listing = JsonUtil.createObjectNode();

    // Mandatory fields:
    this.constructZooplaRequiredField(listingInfo, zooplaBranchId, listing, multiFields,
        isCommercial, isRent);

    // bathrooms
    Integer bathrooms = listingInfo.parseBathrooms();
    if (bathrooms != null) {
      listing.put("bathrooms", bathrooms);
    }

    // living_rooms
    Integer livingRooms = listingInfo.parseReceptionRooms();
    if (livingRooms != null) {
      listing.put("living_rooms", livingRooms);
    }

    // content
    this.constructZooplaMediaContent(listingInfo, listing);

    this.constructZooplaGeneralInfo(multiFields, listing, isCommercial, listingInfo);

    this.constructZooplaSaleInfo(multiFields, listing, isRent);

    this.constructZooplaUtilityInfo(multiFields, listing);

    // feature_list
    this.listingService.constructFeatureList(multiFields, listing, listingInfo, true);

    ListingRightAndRestriction rightAndRestriction = multiFields.getRightAndRestriction();
    if (rightAndRestriction != null) {
      Integer permittedDevelopment = rightAndRestriction.getPermittedDevelopment();
      // known_planning_considerations
      if (permittedDevelopment != null) {
        String considerations = permittedDevelopment == TrueOrFalse.TRUE.getCode() ?
            "No requirements for any known or in progress planning permissions." : "N/A";
        listing.put("known_planning_considerations", considerations);
      }
    }

    // total_bedrooms
    Integer bedrooms = listingInfo.parseBedrooms();
    if (bedrooms != null) {
      listing.put("total_bedrooms", bedrooms);
    }

    // new_home
    if (listingInfo.getNewBuilt() != null) {
      Integer newBuilt = listingInfo.getNewBuilt();
      listing.put("new_home", TrueOrFalse.fromCode(newBuilt) == TrueOrFalse.TRUE);
    }

    // construction_materials
    if (listingInfo.getPropertyConstruction() != null) {
      ArrayNode constructionMaterials = listing.putArray("construction_materials");
      constructionMaterials.add(PropertyConstructionType.fromCode(
          listingInfo.getPropertyConstruction()).getDescription());
    }

    // construction_year
    if (listingInfo.getBuiltYear() != null) {
      listing.put("construction_year", listingInfo.getBuiltYear());
    }

    return listing;
  }

  private void constructZooplaUtilityInfo(ListingMultiFieldsUK multiFields, ObjectNode listing) {
    ListingUtilityInfo utilityInfo = multiFields.getUtilityInfo();
    if (utilityInfo != null) {
      if (StringUtils.isNotEmpty(utilityInfo.getAccessibility())) {
        // accessibility
        ArrayNode accessibility = listing.putArray("accessibility");
        Arrays.stream(utilityInfo.getAccessibility().split(","))
            .map(Integer::parseInt)
            .map(AccessibilityType::convertToZooplaType)
            .forEach(accessibility::add);
      }

      // broadband_supply
      if (StringUtils.isNotEmpty(utilityInfo.getBroadband())) {
        ArrayNode broadbandSupply = listing.putArray("broadband_supply");
        Arrays.stream(utilityInfo.getBroadband().split(","))
            .map(Integer::parseInt)
            .map(BroadbandType::convertToZooplaType)
            .forEach(broadbandSupply::add);
      }

      // electricity_supply
      if (StringUtils.isNotEmpty(utilityInfo.getElectricitySupply())) {
        ArrayNode electricitySupply = listing.putArray("electricity_supply");
        Arrays.stream(utilityInfo.getElectricitySupply().split(","))
            .map(Integer::parseInt)
            .map(ElectricitySupplyType::convertToZooplaType)
            .forEach(typeList -> typeList.forEach(electricitySupply::add));
      }

      // parking
      if (StringUtils.isNotEmpty(utilityInfo.getParking())) {
        ArrayNode parking = listing.putArray("parking");
        Arrays.stream(utilityInfo.getParking().split(","))
            .map(Integer::parseInt)
            .map(ParkingType::convertToZooplaType)
            .flatMap(Collection::stream)
            .distinct()
            .forEach(parking::add);
      }
    }
  }

  private void constructZooplaGeneralInfo(ListingMultiFieldsUK multiFields, ObjectNode listing,
                                          boolean isCommercial, ListingInfo listingInfo) {
    ListingGeneralInfo generalInfo = multiFields.getGeneralInfo();
    if (generalInfo != null) {
      // furnished_state
      if (generalInfo.getFurnished() != null) {
        String zooplaType = FurnishedType.convertToZooplaType(generalInfo.getFurnished());
        listing.put("furnished_state", zooplaType);
      }

      // pets_allowed
      if (generalInfo.getPetAllowed() != null) {
        listing.put("pets_allowed",
            TrueOrFalse.fromCode(generalInfo.getPetAllowed()) == TrueOrFalse.TRUE);
      }

      // local_authority
      if (!isCommercial && StringUtils.isNotEmpty(generalInfo.getCouncilTax())) {
        ObjectNode localAuthority = JsonUtil.createObjectNode();
        localAuthority.put("council_tax_band", generalInfo.getCouncilTax());
        listing.set("local_authority", localAuthority);
      }

      // floor_levels
      if (generalInfo.getEntranceFloor() != null) {
        ArrayNode floorLevels = listing.putArray("floor_levels");
        int entranceFloor = generalInfo.getEntranceFloor().intValue();
        if (listingInfo.getStories() != null
            && listingInfo.getStories().intValue() == entranceFloor) {
          floorLevels.add("penthouse");
        } else if (entranceFloor == 1) {
          floorLevels.add("ground");
        } else if (entranceFloor <= 0) {
          floorLevels.add("basement");
        }

        if (floorLevels.isEmpty()) {
          listing.remove("floor_levels");
        }
      }

      // epc_ratings
      if (StringUtils.isNotEmpty(generalInfo.getEpcRating())) {
        Map<String, Integer> epcMap = Map.of("A", 100,
            "B", 85,
            "C", 75,
            "D", 62,
            "E", 47,
            "F", 29,
            "G", 10);
        Integer epcScore = epcMap.get(generalInfo.getEpcRating());
        if (epcScore != null) {
          ObjectNode epcRatings = JsonUtil.createObjectNode();
          epcRatings.put("eer_current_rating", epcScore.intValue());
          listing.set("epc_ratings", epcRatings);
        }
      }
    }
  }

  private void constructZooplaSaleInfo(ListingMultiFieldsUK multiFields, ObjectNode listing,
                                       boolean isRent) {
    ListingSaleInfo saleInfo = multiFields.getSaleInfo();
    if (saleInfo != null) {
      // deposit
      if (saleInfo.getDeposit() != null) {
        listing.put("deposit",
            saleInfo.getDeposit().setScale(2, RoundingMode.HALF_UP).doubleValue());
      }
      // ground_rent
      if (saleInfo.getAnnualGroundRent() != null) {
        ObjectNode groundRent = JsonUtil.createObjectNode();
        groundRent.put("amount", saleInfo.getAnnualGroundRent()
            .setScale(2, RoundingMode.HALF_UP).doubleValue());
        // date_of_next_review
        if (saleInfo.getGroundReviewDate() != null) {
          groundRent.put("date_of_next_review",
              DatePattern.NORM_DATE_FORMAT.format(saleInfo.getGroundReviewDate()));
        }
        // review_period
        if (saleInfo.getGroundRentReviewFrequency() != null) {
          groundRent.put("review_period",
              saleInfo.getGroundRentReviewFrequency().intValue());
        }
        listing.set("ground_rent", groundRent);
      }
      // available_from_date
      if (isRent && saleInfo.getAvailableFrom() != null) {
        listing.put("available_from_date",
            DatePattern.NORM_DATE_FORMAT.format(saleInfo.getAvailableFrom()));
      }

      // tenure
      if (saleInfo.getTenure() != null) {
        ObjectNode tenure = JsonUtil.createObjectNode();
        String zooplaTenureType = TenureType.convertToZooplaType(saleInfo.getTenure());
        tenure.put("type", zooplaTenureType);
        if (saleInfo.getTenureExpiryDate() != null
            && this.zooplaTenureExpiryDateRequired(zooplaTenureType)) {
          tenure.put("expiry_date",
              DatePattern.NORM_DATE_FORMAT.format(saleInfo.getTenureExpiryDate()));
        }
        // shared_ownership
        if ("leasehold".equals(zooplaTenureType) && saleInfo.getOwnershipShared() != null) {
          ObjectNode sharedOwnership = JsonUtil.createObjectNode();
          sharedOwnership.put("percentage", saleInfo.getOwnershipShared().intValue());
          if (StringUtils.isNotEmpty(saleInfo.getPriceAndChargesDetails())) {
            sharedOwnership.put("details", saleInfo.getPriceAndChargesDetails());
          }
          tenure.set("shared_ownership", sharedOwnership);
        }
        listing.set("tenure", tenure);
      }

      // service_charge
      if (saleInfo.getAnnualServiceCharge() != null) {
        ObjectNode serviceCharge = JsonUtil.createObjectNode();
        double charge = saleInfo.getAnnualServiceCharge()
            .setScale(2, RoundingMode.HALF_UP).doubleValue();
        serviceCharge.put("charge", charge);
        serviceCharge.put("frequency", "per_year");
        listing.set("service_charge", serviceCharge);
      }
    }
  }

  private boolean zooplaTenureExpiryDateRequired(String zooplaTenureType) {
    return "leasehold".equals(zooplaTenureType) || "share_of_freehold".equals(zooplaTenureType);
  }

  private void constructZooplaRequiredField(ListingInfo listingInfo, String zooplaBranchId,
                                            ObjectNode listing, ListingMultiFieldsUK multiFields,
                                            boolean isCommercial, boolean isRent) {
    // Mandatory: branch_reference
    listing.put("branch_reference", zooplaBranchId);
    // Mandatory: listing_reference
    listing.put("listing_reference", zooplaBranchId + "|" + listingInfo.getId());
    // Mandatory: category
    listing.put("category", isCommercial ? "commercial" : "residential");
    // Mandatory: location
    constructZooplaLocation(listingInfo, listing);
    // Mandatory: detailed_description
    ArrayNode detailDescriptions = listing.putArray("detailed_description");
    ObjectNode description = JsonUtil.createObjectNode();
    description.put("heading", "description");
    description.put("text", listingInfo.getDetailsDescribe());
    detailDescriptions.add(description);
    // Mandatory: life_cycle_status
    String zooplaListingStatus =
        ListingStatus.convertToZooplaStatus(listingInfo.getListingStatus());
    listing.put("life_cycle_status", zooplaListingStatus);
    // Mandatory: pricing
    constructZooplaPrice(listingInfo, multiFields, isCommercial, isRent, listing);
    // Mandatory: property_type
    String zooplaPropertyType = PropertyType.convertToZooplaType(listingInfo.getPropertyType());
    listing.put("property_type", zooplaPropertyType);
  }

  private void constructZooplaMediaContent(ListingInfo listingInfo, ObjectNode listing) {
    ArrayNode content = listing.putArray("content");

    if (StringUtils.isNotEmpty(listingInfo.getListingPicturesWithType())) {
      JsonNode pictures = JsonUtil.readTree(listingInfo.getListingPicturesWithType());
      if (pictures != null && pictures.isArray()) {
        for (JsonNode obj : pictures) {
          String mediaType = obj.path("type").asText("picture");
          Map<String, String> mediaTypeMap = Map.of(
              "picture", "image",
              "floorplan", "floor_plan",
              "epc", "epc_graph"
          );
          String zooplaMediaType = mediaTypeMap.getOrDefault(mediaType, "image");
          this.addZooplaMediaNode(content, obj.path("url").asText(), zooplaMediaType, null);
        }
      }
    }

    this.addZooplaMediaNode(content, listingInfo.getVideo(), "virtual_tour", null);
    this.addZooplaMediaNode(content, listingInfo.getVirtualTour(), "virtual_tour", null);
    // Remove content if no elements were added
    if (content.isEmpty()) {
      listing.remove("content");
    }
  }

  private void addZooplaMediaNode(ArrayNode content, String url, String type, String caption) {
    if (StringUtils.isNotEmpty(url)) {
      ObjectNode node = JsonUtil.createObjectNode();
      node.put("url", url);
      node.put("type", type);
      if (caption != null) {
        node.put("caption", caption);
      }
      content.add(node);
    }
  }

  private void constructZooplaAreas(ObjectNode listing, BigDecimal totalSqft) {
    // areas
    ObjectNode areas = JsonUtil.createObjectNode();
    ObjectNode internal = JsonUtil.createObjectNode();
    ObjectNode min = JsonUtil.createObjectNode();
    ObjectNode max = JsonUtil.createObjectNode();
    min.put("value", totalSqft.doubleValue());
    min.put("units", "sq_feet");
    internal.set("minimum", min);
    max.put("value", totalSqft.doubleValue());
    max.put("units", "sq_feet");
    internal.set("maximum", max);
    areas.set("internal", internal);
    listing.set("areas", areas);
  }

  private void constructZooplaLocation(ListingInfo listingInfo, ObjectNode listing) {
    ObjectNode location = JsonUtil.createObjectNode();
    location.put("street_name", listingInfo.getStreetAddress1());
    location.put("town_or_city",
        listingInfo.getCity() == null ? "N/A" : listingInfo.getCity());
    location.put("postal_code", listingInfo.getZipCode());
    location.put("country_code", "GB");
    if (StringUtils.isNotEmpty(listingInfo.getLatitude())
        && StringUtils.isNotEmpty(listingInfo.getLongitude())) {
      // key: coordinates
      ObjectNode coordinates = JsonUtil.createObjectNode();
      coordinates.put("latitude", Double.parseDouble(listingInfo.getLatitude()));
      coordinates.put("longitude", Double.parseDouble(listingInfo.getLongitude()));
      location.set("coordinates", coordinates);
    }
    listing.set("location", location);
  }

  private void constructZooplaPrice(ListingInfo listingInfo, ListingMultiFieldsUK multiFields,
                                    boolean isCommercial, boolean isRent, ObjectNode listing) {
    ObjectNode price = JsonUtil.createObjectNode();
    price.put("transaction_type", isRent ? "rent" : "sale");
    price.put("currency_code", "GBP");
    BigDecimal listingPrice = listingInfo.getPrice();
    ListingSaleInfo saleInfo = multiFields.getSaleInfo();
    Integer rentalFrequencyCode = saleInfo.getRentalFrequency();

    if (isRent && rentalFrequencyCode != null) {
      String zooplaRentFrequency = RentalFrequencyType.convertToZooplaType(rentalFrequencyCode);
      price.put("rent_frequency", zooplaRentFrequency);
    }

    // pricePerUnitArea key: price_per_unit_area
    ObjectNode pricePerUnitArea = JsonUtil.createObjectNode();
    BigDecimal totalSqft = listingInfo.getTotalBuildingSqft();
    if (isRent && rentalFrequencyCode != null &&
        rentalFrequencyCode == RentalFrequencyType.BIANNUAL.getCode()) {
      listingPrice = listingPrice.divide(new BigDecimal(2), 2, RoundingMode.HALF_UP);
    }
    if (isCommercial && totalSqft != null) {
      // areas
      constructZooplaAreas(listing, totalSqft);
      BigDecimal pricePerSqft = listingPrice.divide(totalSqft, 2, RoundingMode.HALF_UP);
      // commercial && sale
      pricePerUnitArea.put("price", pricePerSqft.doubleValue());
      pricePerUnitArea.put("units", "sq_feet");
      price.set("price_per_unit_area", pricePerUnitArea);
    }

    price.put("price", listingPrice.setScale(2, RoundingMode.HALF_UP).doubleValue());

    listing.set("pricing", price);
  }

  @Override
  public Future<Boolean> removeFromZoopla(ListingInfo info, ListingInfo oldInfo) {
    CompletableFuture<Boolean> cf = new CompletableFuture<>();
    // Check if has been published to zoopla before
    if (oldInfo == null) {
      cf.complete(true);
      return cf;
    }
    ListingMultiFieldsUK oldMultiFields = listingInfoService.getListingMultiFields(oldInfo);
    if (oldMultiFields.getThirdPartyInfo() == null
        || oldMultiFields.getThirdPartyInfo().getZooplaPublishDate() == null) {
      cf.complete(true);
      return cf;
    }

    // Check if the zooplaBranchId exists
    Long id = oldInfo.getId();
    // check zoopla branchId exists
    long teamId = Long.parseLong(oldInfo.getMlsOrgId());
    long officeId = Long.parseLong(oldInfo.getAgentOrganizationId());
    String branchId = listingService.getBranchId(teamId, officeId,
        IntegrationType.ZOOPLA.getCode());
    if (StringUtils.isEmpty(branchId)) {
      logger.error("zooplaBranchId is empty, teamId={}, officeId:{}, listingId={}",
          teamId, officeId, id);
      cf.complete(false);
      return cf;
    }
    String reason = this.generateZooplaRemoveReason(info.getListingStatus());

    // remove from zoopla
    String zooplaListingId = branchId + "|" + id;
    String listingReference = JsonUtil.createObjectNode()
        .put("listing_reference", zooplaListingId)
        .put("deletion_reason", reason).toString();
    try {
      ZooplaDeleteResp zooplaDeleteResp = zooplaFeignClient.deleteListing(listingReference);
      if (zooplaDeleteResp == null || !"OK".equals(zooplaDeleteResp.getStatus())) {
        logger.error("removeFromZoopla error, teamId={}, listingReference={}, zooplaResp={}",
            teamId, zooplaListingId, zooplaDeleteResp);
        cf.complete(false);
      } else {
        // update the thirdPartyInfo
        cf.complete(true);
      }
      return cf;
    } catch (ListingHubException e) {
      logger.error("removeFromZoopla error, listingReference:{}", listingReference);
      cf.complete(false);
      return cf;
    }
  }

  @Override
  public void createLead(ZooplaApplicantDto zooplaLead) {
    // convert to crm Lead
    ZooplaApplicantDto.ListingDetails listingDetails = zooplaLead.getListingDetails();
    if (listingDetails == null) {
      return;
    }
    String sourceId = listingDetails.getSourceId();
    long listingId = Long.parseLong(sourceId.split("\\|")[1]);
    ListingInfo listingInfo = listingInfoService.getListingInfoById(listingId);
    if (listingInfo == null) {
      return;
    }
    long agentId = Long.parseLong(listingInfo.getPrimaryAgentId());

    String teamId = listingInfo.getMlsOrgId();
    String officeId = String.valueOf(userManager.getUserOfficeId(agentId));
    // primary agent integration -> ownership
    ListingIntegrationInfo integrationInfo = integrationInfoDao
        .findBranchIdByTeamAndOffice(teamId, officeId, IntegrationType.ZOOPLA.getCode());
    // source integrationInfo
    ListingIntegrationInfo sourceIntegrationInfo = integrationInfoDao.findBranchIdByTeamAndOffice(
        teamId, listingInfo.getAgentOrganizationId(), IntegrationType.ZOOPLA.getCode());
    if (integrationInfo == null || sourceIntegrationInfo == null) {
      return;
    }
    LeadAddRequestBo leadBo = this.buildLeadAddRequestBo(zooplaLead, integrationInfo, agentId, sourceIntegrationInfo);
    String listingUrl = zooplaLead.getListingDetails().getUrl();
    List<LeadPropertyVoBo> propertyBos =
        List.of(listingHubLeadService.buildListingDetail(listingUrl, listingInfo));
    listingHubLeadService.thirdPartyCreateLead(leadBo, propertyBos, List.of(listingInfo),
        Map.of(listingInfo.getId(), new Pair<>(listingInfo, new String[]{listingUrl, null, null})),
        IntegrationType.ZOOPLA);
  }

  private LeadAddRequestBo buildLeadAddRequestBo(ZooplaApplicantDto zooplaLead,
                                                 ListingIntegrationInfo info, long agentId,
                                                 ListingIntegrationInfo sourceIntegrationInfo) {
    LeadAddRequestBo leadBo = listingHubLeadService.initLeadAddRequestBo(agentId,
        IntegrationType.ZOOPLA, info, sourceIntegrationInfo);
    ZooplaApplicantDto.Contact zooplaLeadDetail = zooplaLead.getContact();
    // leadInfo
    leadBo.setFirstName(zooplaLeadDetail.getFirstName());
    leadBo.setLastName(zooplaLeadDetail.getLastName());
    UserPhoneBo userPhoneBo = new UserPhoneBo();
    userPhoneBo.setPhone(zooplaLeadDetail.getPhoneNumber());
    leadBo.getUserPhoneModels().add(userPhoneBo);
    leadBo.setEmail(zooplaLeadDetail.getEmail());
    //leadType
    leadBo.getLeadTypes().add(ZooplaApplicantDto.Intent.toCrmLeadType(zooplaLead.getIntent()));
    // searchCriteria
    leadBo.setLeadInquiries(this.buildSearchCriteria(zooplaLead.getSearchCriteria()));
    return leadBo;
  }

  private LeadPropertyVoBo buildListingDetail(ZooplaApplicantDto.ListingDetails listingDetails,
                                              long listingId) {
    LeadPropertyVoBo propertyVoBo = new LeadPropertyVoBo();
    propertyVoBo.setLabel("buy-high interest");
    propertyVoBo.setListingId(String.valueOf(listingId));
    // listing detail
    if (listingDetails != null) {
      // address
      ZooplaApplicantDto.ListingDetails.Address address = listingDetails.getAddress();
      if (address != null) {
        String listingAddress = Stream.of(address.getStreetName(), address.getTownOrCity(),
                address.getCountryCode(), address.getPostcode())
            .filter(StringUtils::isNotBlank).collect(Collectors.joining(", "));
        propertyVoBo.setStreetAddress(listingAddress);
      }

      // price
      ZooplaApplicantDto.ListingDetails.Pricing pricing = listingDetails.getPricing();
      if (pricing != null && pricing.getPrice() != null) {
        propertyVoBo.setPrice(pricing.getPrice().longValue());
      }
      // propertyType
      String crmPropertyType = ZooplaApplicantDto.ZooplaPropertyType
          .toCrmPropertyType(listingDetails.getPropertyType());
      propertyVoBo.setPropertyType(crmPropertyType);
      // bedrooms
      Integer bedrooms = listingDetails.getBedrooms();
      if (bedrooms != null) {
        propertyVoBo.setBedRooms(bedrooms);
      }
      // bathrooms
      Integer bathrooms = listingDetails.getBathrooms();
      if (bathrooms != null) {
        propertyVoBo.setBathRooms(bathrooms);
      }
      // url
      String url = listingDetails.getUrl();
      propertyVoBo.setNote("Url: " + url);
    }
    return propertyVoBo;
  }

  private LeadInquiriesBo buildSearchCriteria(ZooplaApplicantDto.SearchCriteria searchCriteria) {
    LeadInquiriesBo inquiriesBo = new LeadInquiriesBo();
    inquiriesBo.setLocations(new ArrayList<>());
    // search Criteria
    if (searchCriteria != null) {
      ZooplaApplicantDto.SearchCriteria.Location location = searchCriteria.getLocation();
      if (location != null) {
        // location
        if (StringUtils.isNotEmpty(location.getAreaStreet())) {
          inquiriesBo.getLocations().add(
              new LocationElemBo(LocationElemBo.TYPESTREETADDRESS, location.getAreaStreet(), null)
          );
        }
        if (StringUtils.isNotEmpty(location.getPostTown())) {
          inquiriesBo.getLocations().add(
              new LocationElemBo(LocationElemBo.TYPECITY, location.getPostTown(), null)
          );
        }
        if (StringUtils.isNotEmpty(location.getCountyArea())) {
          inquiriesBo.getLocations().add(
              new LocationElemBo(LocationElemBo.TYPECOUNTY, location.getCountyArea(), null)
          );
        }
        if (StringUtils.isNotEmpty(location.getOutcode())
            && StringUtils.isNotEmpty(location.getIncode())) {
          String zipcode = location.getOutcode() + " " + location.getIncode();
          inquiriesBo.getLocations().add(
              new LocationElemBo(LocationElemBo.TYPEZIPCODE, zipcode, null)
          );
        }
      }
      // minBedrooms
      inquiriesBo.setBedroomsMin(searchCriteria.getMinimumBedrooms() == null ?
          -1 : searchCriteria.getMinimumBedrooms());

      // minPrice
      if (searchCriteria.getMinimumPrice() != null) {
        inquiriesBo.setPriceMin(searchCriteria.getMinimumPrice().longValue());
      } else {
        inquiriesBo.setPriceMin(-1L);
      }
      // maxPrice
      if (searchCriteria.getMaximumPrice() != null) {
        inquiriesBo.setPriceMax(searchCriteria.getMaximumPrice().longValue());
      } else {
        inquiriesBo.setPriceMax(-1L);
      }
      // propertyType
      if (searchCriteria.getPropertyType() != null) {
        String crmPropertyType = ZooplaApplicantDto.ZooplaPropertyType
            .toCrmPropertyType(searchCriteria.getPropertyType());
        inquiriesBo.setPropertyType(crmPropertyType);
      }
    }
    // minBathRooms
    inquiriesBo.setBathroomsMin("-1");
    return inquiriesBo;
  }

  /**
   * @param listingStatus listingStatus
   * @return removeReason
   */
  private String generateZooplaRemoveReason(Integer listingStatus) {
    String defaultReason = "exchanged";
    if (listingStatus == null) {
      return defaultReason;
    }
    ListingStatus status = ListingStatus.fromCode(listingStatus);
    return switch (status) {
      case SOLD -> "completed";
      case LET -> "let";
      case WITHDRAWN -> "withdrawn";
      default -> defaultReason;
    };
  }
}
