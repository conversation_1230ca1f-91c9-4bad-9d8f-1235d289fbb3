package com.homethy.service.impl;

import com.homethy.dao.listing.ListingInfoDao;
import com.homethy.dao.listing.ListingUpdateInfoDao;
import com.homethy.enums.ListingStatus;
import com.homethy.enums.ListingUpdateType;
import com.homethy.model.ListingInfo;
import com.homethy.model.ListingMultiFieldsUK;
import com.homethy.model.ListingUpdateInfo;
import com.homethy.service.ListingInfoService;
import com.homethy.util.JsonUtil;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Description
 * @create 2024-10-09 14:49
 */
@Service
public class ListingInfoServiceImpl implements ListingInfoService {
  private static final Logger logger = LoggerFactory.getLogger(ListingInfoServiceImpl.class);

  @Autowired
  private ListingInfoDao listingInfoDao;

  @Autowired
  private ListingUpdateInfoDao listingUpdateInfoDao;

  @Override
  public ListingInfo createOrUpdateListingInfo(ListingInfo property, ListingInfo oldListingInfo) {
    boolean createFlag = property.getId() == null;
    if (createFlag) {
      listingInfoDao.insertListingInfo(property);
      logger.info("Insert listing-listingId:{}", property.getId());
    } else {
      //update or delete
      long listingId = property.getId();
      Integer marketStatus = property.getMarketStatus();
      logger.info("Update listing-listingId:{}, marketStatus: {}", listingId, marketStatus);
      listingInfoDao.updateListingInfoById(property);
    }
    // ID为空或者旧数据不为空，即新增或者创建时，需要添加updateInfo，否则不需要
    if (createFlag || oldListingInfo != null) {
      this.addListingUpdateInfo(property, oldListingInfo);
    }
    return property;
  }

  @Override
  public ListingInfo getListingInfoById(long id) {
    Optional<ListingInfo> listingInfo = listingInfoDao.findListingInfoById(id);
    return listingInfo.orElseThrow(() -> new RuntimeException("Property not exist"));
  }

  @Override
  public ListingInfo findByIdAndMarketStatus(long id, int marketStatus) {
    return listingInfoDao.findListingInfoByPrimaryKey(id, marketStatus);
  }

  @Override
  public List<ListingUpdateInfo> getListingUpdateInfos(long listingId) {
    return listingUpdateInfoDao.getListingUpdateInfosById(String.valueOf(listingId));
  }

  @Override
  public ListingMultiFieldsUK getListingMultiFields(ListingInfo info) {
    if (info != null) {
      String multiFields = info.getMultiFields();
      if (StringUtils.isNotEmpty(multiFields)) {
        return JsonUtil.toBean(multiFields, ListingMultiFieldsUK.class);
      }
      logger.warn("MultiFields is empty: listingId={}", info.getId());
    }
    return ListingMultiFieldsUK.builder().build();
  }

  private void addListingUpdateInfo(ListingInfo newListingInfo, ListingInfo oldListingInfo) {
    if (oldListingInfo == null) {
      //create new ListingInfo
      ListingUpdateInfo listingUpdateInfo = ListingUpdateInfo.builder()
          .mlsListingId(String.valueOf(newListingInfo.getId()))
          .updateType(ListingUpdateType.ELECTRIC_CENTRAL.getCode())
          .newValue(null)
          .curPrice(String.valueOf(newListingInfo.getActualPrice()))
          .curStatus(ListingStatus.fromCode(newListingInfo.getListingStatus()))
          .mlsOrgId(Long.parseLong(newListingInfo.getMlsOrgId()))
          .build();
      listingUpdateInfoDao.insert(listingUpdateInfo);
    } else {
      //update old ListingInfo
      if (!Objects.equals(oldListingInfo.getActualPrice(), newListingInfo.getActualPrice())) {
        //price change
        ListingUpdateInfo priceUpdateInfo = ListingUpdateInfo.builder()
            .mlsListingId(String.valueOf(newListingInfo.getId()))
            .updateType(ListingUpdateType.ROOM_HEATING.getCode())
            .newValue(String.valueOf(newListingInfo.getActualPrice()))
            .curPrice(String.valueOf(oldListingInfo.getActualPrice()))
            .curStatus(ListingStatus.fromCode(oldListingInfo.getListingStatus()))
            .mlsOrgId(Long.parseLong(newListingInfo.getMlsOrgId()))
            .build();
        listingUpdateInfoDao.insert(priceUpdateInfo);
      }
      if (!Objects.equals(oldListingInfo.getListingStatus(), newListingInfo.getListingStatus())) {
        //status change
        ListingUpdateInfo statusUpdateInfo = ListingUpdateInfo.builder()
            .mlsListingId(String.valueOf(newListingInfo.getId()))
            .updateType(ListingUpdateType.COMMUNAL_HEATING_SYSTEMS.getCode())
            .newValue(ListingStatus.fromCode(newListingInfo.getListingStatus()).toString())
            .curPrice(String.valueOf(oldListingInfo.getActualPrice()))
            .curStatus(ListingStatus.fromCode(oldListingInfo.getListingStatus()))
            .mlsOrgId(Long.parseLong(newListingInfo.getMlsOrgId()))
            .build();
        listingUpdateInfoDao.insert(statusUpdateInfo);
      }
    }
  }
}
