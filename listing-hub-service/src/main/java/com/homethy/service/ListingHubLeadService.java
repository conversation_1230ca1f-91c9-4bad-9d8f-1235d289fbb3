package com.homethy.service;

import com.homethy.enums.IntegrationType;
import com.homethy.enums.PropertyType;
import com.homethy.lead.timeline.model.LeadTimelineTypeEnum;
import com.homethy.manager.UserManager;
import com.homethy.microservice.client.bo.persistence.TeamStage;
import com.homethy.microservice.client.lead.LeadKernelService;
import com.homethy.microservice.client.lead.api.LeadAddService;
import com.homethy.microservice.client.lead.apiV3.LeadPropertyService;
import com.homethy.microservice.client.model.ErrorCodeEnumBo;
import com.homethy.microservice.client.model.LeadAddRequestBo;
import com.homethy.microservice.client.model.LeadBo;
import com.homethy.microservice.client.model.LeadPropertyVoBo;
import com.homethy.microservice.client.model.LeadTimelineAddRequestBo;
import com.homethy.microservice.client.model.LeadTimelineFromToTypeEnumBo;
import com.homethy.microservice.client.model.NotifyInfoBo;
import com.homethy.microservice.client.model.Pair;
import com.homethy.microservice.client.model.SourceInfoBo;
import com.homethy.microservice.client.timeline.LeadTimelineAddClient;
import com.homethy.microservice.model.Result;
import com.homethy.model.ListingInfo;
import com.homethy.model.ListingIntegrationInfo;
import com.homethy.util.JsonUtil;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Description
 * @create 2025-01-24 9:47
 */
@Service
@Slf4j
public class ListingHubLeadService {
  @Autowired
  private LeadAddService leadAddService;

  @Autowired
  private LeadPropertyService leadPropertyService;

  @Autowired
  private LeadKernelService leadkernelService;

  @Autowired
  private LeadTimelineAddClient leadTimelineAddClient;

  /**
   * create lead, merge lead, add listings, add timeline
   *
   * @param leadBo         crm lead
   * @param propertyVoBos  crm listings
   * @param listingInfos   lofty listings
   * @param listingInfoMap key: propertyId, value: ListingInfo, {url, moving_reason, comments}
   */
  public void thirdPartyCreateLead(LeadAddRequestBo leadBo, List<LeadPropertyVoBo> propertyVoBos,
                                   List<ListingInfo> listingInfos,
                                   Map<Long, Pair<ListingInfo, String[]>> listingInfoMap,
                                   IntegrationType type) {
    log.info("thirdPartyCreateLead, leadBo: {}", leadBo);
    Result<LeadBo> result = leadAddService.addLeadV2(leadBo);
    long agentId = leadBo.getOwnerId();
    LeadBo lead = result.getData();
    log.info("addLead resp: {}", JsonUtil.toJson(result));
    if (result.getErrorData() != null && result.getErrorData().has("leadId")) {
      long leadId = result.getErrorData().get("leadId").asLong();
      log.info("addLeadV2 fail, leadId={}", leadId);
      lead = leadkernelService.getLeadById(leadId);
      // filter out mailing address
      List<LeadPropertyVoBo> normalProperty
          = propertyVoBos.stream().filter(p -> !p.isMailAddress()).toList();
      leadBo.setLeadProperties(normalProperty);
      leadBo.setLeadUserId(lead.getLeadUserId());
      try {
        leadAddService.updateUkThirdpartyLeadDup(leadBo);
      } catch (Exception e) {
        log.error("add systemNote error.", e);
      }
    }
    long leadId = lead.getId();
    long leadUserId = lead.getLeadUserId();
    // listingInfos
    this.addLeadProperties(propertyVoBos, agentId, leadUserId, leadId);
    // timeline
    for (ListingInfo listingInfo : listingInfos) {
      String listingUrl = listingInfoMap.get(listingInfo.getId()).getValue()[0];
      Map<String, Object> content = this.buildLeadTimelineContent(listingInfo, listingUrl, type);
      LeadTimelineAddRequestBo timelineBo = this.buildLeadTimelineBo(agentId, leadId, content);
      leadTimelineAddClient.addLeadTimeline(timelineBo);
    }
  }

  private static String replaceCommaAndSpaceToComma(String str) {
    return StringUtils.isEmpty(str) ? str : str.replaceAll(",\\s+", ",");
  }

  public LeadPropertyVoBo buildListingDetail(String url, ListingInfo listingInfo) {
    LeadPropertyVoBo propertyVoBo = new LeadPropertyVoBo();
    propertyVoBo.setLabel("High Interest");
    propertyVoBo.setListingId(String.valueOf(listingInfo.getId()));
    propertyVoBo.setPropertyType(
        PropertyType.fromCode(listingInfo.getPropertyType()).getDescription());
    // address
    propertyVoBo.setStreetAddress(replaceCommaAndSpaceToComma(listingInfo.getStreetAddress1()));
    propertyVoBo.setAddress2(replaceCommaAndSpaceToComma(listingInfo.getStreetAddress2()));
    propertyVoBo.setZipcode(listingInfo.getZipCode());
    // propertyVoBo.setState(listingInfo.getCounty());
    propertyVoBo.setCity(listingInfo.getCity());
    propertyVoBo.setCounty(listingInfo.getCounty());

    propertyVoBo.setPrice(listingInfo.getPrice().longValue());

    // bedrooms
    propertyVoBo.setBedRooms(listingInfo.parseBedrooms() != null ? listingInfo.parseBedrooms() :
        -1);
    // bathrooms
    propertyVoBo.setBathRooms(listingInfo.parseBathrooms() != null ? listingInfo.parseBathrooms() :
        -1);
    // url
    propertyVoBo.setNote("Url: " + url);
    return propertyVoBo;
  }

  /**
   *
   * @param agentId primary agent
   * @param type ZOOPLA, RIGHT_MOVE
   * @param ownerShipIntegrationInfo -> ownerShip -> primary agent
   * @param sourceIntegrationInfo -> source -> listingInfo
   * @return crm leadAddBo
   */
  public LeadAddRequestBo initLeadAddRequestBo(long agentId, IntegrationType type,
                                               ListingIntegrationInfo ownerShipIntegrationInfo,
                                               ListingIntegrationInfo sourceIntegrationInfo) {
    LeadAddRequestBo leadBo = new LeadAddRequestBo();
    leadBo.setTimelineContentMap(Map.of("source", type.getDescription()));
    leadBo.setAddTimeline(true);
    leadBo.setThrowIfDuplicate(true);
    leadBo.setOwnership(ownerShipIntegrationInfo.getOwnership());
    // stageId
    leadBo.setStageId(TeamStage.StageType.NewLead.getType());
    // source
    SourceInfoBo sourceInfoBo = new SourceInfoBo();
    sourceInfoBo.setSource(sourceIntegrationInfo.getSourceId());
    sourceInfoBo.setRealSource(type.getRealSource());
    leadBo.setSourceInfo(sourceInfoBo);
    // send welcome email
    NotifyInfoBo notifyInfoBo = new NotifyInfoBo(true, true,
        true, true, sourceIntegrationInfo.isSendWelcomeEmail(),
        sourceIntegrationInfo.isSendWelcomeEmail(), true);
    leadBo.setNotifyInfo(notifyInfoBo);
    // agentId
    leadBo.setOwnerId(agentId);
    leadBo.setLeadTypes(new ArrayList<>());
    leadBo.setUserPhones(new ArrayList<>());
    leadBo.setUserPhoneModels(new ArrayList<>());
    return leadBo;
  }

  private void addLeadProperties(List<LeadPropertyVoBo> propertyVoBos, long agentId,
                                    long leadUserId, long leadId) {
    try {
      ErrorCodeEnumBo resp = leadPropertyService.handlerProperties(propertyVoBos, agentId,
          leadUserId, leadId, "add", false);
      log.info("add property resp:{}", resp);
    } catch (Exception e) {
      log.info("createLeadProperties error, properties={}, agentId={}, leadUserId={}, leadId={}",
          propertyVoBos, agentId, leadUserId, leadId);
    }
  }


  private Map<String, Object> buildLeadTimelineContent(ListingInfo info, String propertyUrl,
                                                       IntegrationType type) {
    Map<String, Object> content = new HashMap<>();
    long price = info.getPrice().longValue();
    String address = info.getFullListingAddressWithAddress2();
    content.put("listingId", info.getId());
    content.put("price", price);
    content.put("propertyAddress", address);
    content.put("clientTrackingContent", price + ", " + address);
    content.put("propertyUrl", propertyUrl);
    content.put("engageType", "Viewed");
    if (type == IntegrationType.ZOOPLA) {
      content.put("listingMedia", "Zoopla property");
    } else if (type == IntegrationType.RIGHT_MOVE) {
      content.put("listingMedia", "Rightmove property");
    }
    return content;
  }

  private LeadTimelineAddRequestBo buildLeadTimelineBo(long agentId, long leadId,
                                                       Map<String, Object> content) {
    LeadTimelineAddRequestBo timelineBo = new LeadTimelineAddRequestBo();
    timelineBo.setAgentId(agentId);
    timelineBo.setLeadId(leadId);
    timelineBo.setTimelineType(LeadTimelineTypeEnum.THIRD_PARTY_VIEW_LISTING.getType());
    timelineBo.setTimelineTime(new Date());
    timelineBo.setFromId(leadId);
    timelineBo.setFromType(LeadTimelineFromToTypeEnumBo.Lead.getType());
    timelineBo.setToId(agentId);
    timelineBo.setToType(LeadTimelineFromToTypeEnumBo.Agent.getType());
    timelineBo.setContentMap(JsonUtil.toJson(content));
    return timelineBo;
  }
}
