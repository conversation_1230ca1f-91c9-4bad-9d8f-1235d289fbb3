package com.homethy.service;

import com.homethy.model.ListingInfo;
import com.homethy.model.dto.ZooplaApplicantDto;

import org.springframework.scheduling.annotation.Async;

import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * @date 2024/10/31 13:26
 */
public interface ZooplaService {

  /**
   *
   * @param info listingInfo
   * @return url: success; null: failed
   */
  @Async("asyncExecutor")
  Future<String> sendToZoopla(ListingInfo info);

  /**
   *
   * @param info new listing
   * @param oldInfo old listing
   * @return true: success, false: failed
   */
  @Async("asyncExecutor")
  Future<Boolean> removeFromZoopla(ListingInfo info, ListingInfo oldInfo);

  void createLead(ZooplaApplicantDto zooplaLead);
}
