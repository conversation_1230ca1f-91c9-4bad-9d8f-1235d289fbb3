package com.homethy.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/11/28 17:19
 */
@FeignClient(
    name = "searchCenterClient",
    url = "${microservice.listing-core.url:listing-core.site.svc:80}/api"
)
@Component
public interface SearchCenterClient {
  @PostMapping("/crm-search-v2/{siteId}")
  String crmSearchV2(@PathVariable("siteId") long siteId, @RequestBody Map<String, Object> params);
}
