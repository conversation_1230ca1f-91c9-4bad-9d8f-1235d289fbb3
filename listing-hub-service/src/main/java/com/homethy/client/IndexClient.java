package com.homethy.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

@FeignClient(name = "index", url = "${index.url:http://10.20.133.60:8765}")
public interface IndexClient {
  @PostMapping(value = "/startDataIndexWithDefaultSettings", consumes = {"application/json"}, produces = {"application/json"})
  Map<String, Object> index(@RequestBody Map<String, Object> indexRequest);
}