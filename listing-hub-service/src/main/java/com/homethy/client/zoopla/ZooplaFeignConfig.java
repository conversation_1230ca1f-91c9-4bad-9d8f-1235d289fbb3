package com.homethy.client.zoopla;


import com.fasterxml.jackson.databind.node.ObjectNode;
import com.homethy.exception.ErrorCodeEnum;
import com.homethy.exception.ListingHubException;
import com.homethy.util.JsonUtil;

import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.KeyManagementException;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertificateException;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

import javax.net.ssl.SSLContext;

import feign.Client;
import feign.RequestInterceptor;
import feign.codec.ErrorDecoder;
import feign.httpclient.ApacheHttpClient;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Description
 * @create 2024-11-05 13:50
 */
@Slf4j
public class ZooplaFeignConfig {
  @Value("${ZOOPLA_PASSPHRASE}")
  private String passphrase;

  @Value("${zoopla.keystorePath}")
  private String keystorePath;

  private SSLContext createSSLContext()
      throws KeyStoreException, IOException, NoSuchAlgorithmException, CertificateException,
      UnrecoverableKeyException, KeyManagementException {
    KeyStore keyStore = KeyStore.getInstance("PKCS12");
    try (InputStream keystoreStream = new FileInputStream(keystorePath)) {
      keyStore.load(keystoreStream, passphrase.toCharArray());
    }

    return SSLContextBuilder.create()
        .loadKeyMaterial(keyStore, passphrase.toCharArray())
        .build();
  }

  @Bean
  public Client feignClient() throws UnrecoverableKeyException,
      CertificateException,
      NoSuchAlgorithmException,
      KeyStoreException,
      IOException,
      KeyManagementException {
    SSLContext sslContext = this.createSSLContext();

    SSLConnectionSocketFactory socketFactory = new SSLConnectionSocketFactory(sslContext);
    CloseableHttpClient httpClient = HttpClients.custom()
        .setSSLSocketFactory(socketFactory)
        .evictExpiredConnections()
        .build();

    return new ApacheHttpClient(httpClient);
  }

  @Bean
  public RequestInterceptor zpgListingRequestInterceptor() {
    return template -> {
      // set zoopla request headers
      String url = template.url();
      String eTag = getNowTimeStr();

      // Mapping URL path keywords to Content-Type values
      Map<String, String> contentTypeMapping = Map.of(
          "/listing/update", "application/json; profile=\"https://realtime-listings.webservices"
              + ".zpg.co.uk/docs/v2.3/schemas/listing/update.json\"",
          "/listing/list", "application/json; profile=\"https://realtime-listings.webservices.zpg"
              + ".co.uk/docs/v2.3/schemas/listing/list.json\"",
          "/listing/delete", "application/json; profile=\"https://realtime-listings.webservices"
              + ".zpg.co.uk/docs/v2.3/schemas/listing/delete.json\""
      );

      // Set Content-Type header based on URL path and apply ZPG-Listing-ETag for update
      contentTypeMapping.forEach((path, contentType) -> {
        if (url.contains(path)) {
          template.header("Content-Type", contentType);
          if ("/listing/update".equals(path)) {
            template.header("ZPG-Listing-ETag", eTag);
          }
        }
      });
    };
  }

  private String getNowTimeStr() {
    ZonedDateTime now = ZonedDateTime.now();
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSX");
    return now.format(formatter);
  }

  @Bean
  public ErrorDecoder errorDecoder() {
    return (methodKey, response) -> {
      try {
        String errorContent = new String(
            response.body().asInputStream().readAllBytes(), StandardCharsets.UTF_8);
        ObjectNode zooplaError = JsonUtil.toBean(errorContent, ObjectNode.class);
        log.error("Zoopla API Error: {}, method: {}", zooplaError, methodKey);
        return new ListingHubException(ErrorCodeEnum.ZOOPLA_ERROR, zooplaError);
      } catch (Exception e) {
        log.error("Zoopla decode error", e);
        return new ListingHubException(ErrorCodeEnum.ZOOPLA_ERROR);
      }
    };
  }
}
