package com.homethy.client.zoopla;

import com.homethy.exception.ListingHubException;
import com.homethy.model.zoopla.ZooplaDeleteResp;
import com.homethy.model.zoopla.ZooplaPublishResp;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @Description
 * @create 2024-11-05 13:53
 */
@FeignClient(name = "zoopla", url = "${zoopla.url:https://realtime-listings-api.webservices.zpg"
    + ".co.uk/sandbox/v2}", configuration = ZooplaFeignConfig.class)
public interface ZooplaFeignClient {
  @PostMapping("/listing/update")
  ZooplaPublishResp postListings(@RequestBody String property) throws ListingHubException;

  @PostMapping("/listing/list")
  String listListings(@RequestBody String branchId) throws ListingHubException;

  @PostMapping("/listing/delete")
  ZooplaDeleteResp deleteListing(@RequestBody String listingId) throws ListingHubException;
}
