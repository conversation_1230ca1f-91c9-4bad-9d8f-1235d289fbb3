package com.homethy.client;

import com.homethy.model.dto.SearchCondition;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2024/12/2 13:42
 */
@FeignClient(
    name = "listingmgmtClient",
    url = "${microservice.listingmgmt.url:listingmgmt.crm.svc:8080}"
)
@Component
public interface ListingmgmtClient {
  @PostMapping("/admin/listingmgmt/crm-search/{siteId}/searchListingWithLead")
  String searchListingWithLead(@PathVariable("siteId") long siteId,
                               @RequestBody SearchCondition searchCondition);
}
