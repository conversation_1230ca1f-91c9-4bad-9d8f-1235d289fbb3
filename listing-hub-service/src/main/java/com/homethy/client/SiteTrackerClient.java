package com.homethy.client;


import com.homethy.config.CrmFeignClientsConfiguration;
import com.homethy.model.dto.DailyListingViewDto;
import com.homethy.util.JsonUtil;

import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> min.li
 * @date : 2021-10-28
 */
@FeignClient(
        name = "${microservice.site-tracker-server.service.id:site-tracker-server}",
        url = "${microservice.site-tracker-server.url:site-tracker-server.site.svc:8080}",
        path = "/site-tracking",
        configuration = CrmFeignClientsConfiguration.class,
        fallbackFactory = SiteTrackerClient.SiteTrackerClientFallbackFactory.class
)
public interface SiteTrackerClient {

  @PostMapping(value = "/listing-views", headers = {"Content-Type=application/json"})
  Map<Long, Long> getListingViews(@RequestBody Map<String, Object> requestMap);

  @PostMapping(value = "/listing-views-pv", headers = {"Content-Type=application/json"})
  Map<Long, List<DailyListingViewDto>> getDailyListingViews(@RequestBody Map<String, Object> requestMap);


  @Slf4j
  @Component
  class SiteTrackerClientFallbackFactory implements FallbackFactory<SiteTrackerClient> {

    @Override
    public SiteTrackerClient create(Throwable cause) {
      return new SiteTrackerClient() {
        @Override
        public Map<Long, Long> getListingViews(Map<String, Object> requestMap) {
          log.error("getListingViews fails. request:{}", JsonUtil.toJson(requestMap), cause);
          return new HashMap<>();
        }

        @Override
        public Map<Long, List<DailyListingViewDto>> getDailyListingViews(Map<String, Object> requestMap) {
          log.error("getDailyListingViews fails. request:{}", JsonUtil.toJson(requestMap), cause);
          return new HashMap<>();
        }
      };
    }
  }
}
