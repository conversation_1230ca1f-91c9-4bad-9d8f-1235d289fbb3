package com.homethy.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;

import feign.RequestInterceptor;
import feign.RequestTemplate;

/**
 * Created by limin on 2019/11/16.
 * <p>
 * Because call CRM need token, custom FeignClientsConfiguration
 */
public class CrmFeignClientsConfiguration {
  @Value("${crm.service.token:3Bearer eyJhbGciOiJIUzI1NiJ9"
      + ".eyJzdWIiOiJzaXRlIiwiYXVkIjoiOSIsImV4cCI6MTU3Nzk3Mjk2MiwiaWF0IjoxNTQ2NDM2OTYyfQ"
      + ".q6f4U7SRNCpFrdglKLQgeHIAxlFB_oaWzXRh01lHYA}")
  private String token;

  @Bean
  public RequestInterceptor requestInterceptor() {
    return (RequestTemplate requestTemplate) -> requestTemplate.header("Authorization", token);
  }
}
