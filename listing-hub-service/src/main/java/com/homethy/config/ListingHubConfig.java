package com.homethy.config;

import com.homethy.configcenter.client.ReloadableProperties;
import com.homethy.configcenter.client.ReloadablePropertiesFactory;

import java.util.Properties;

/**
 * <AUTHOR>
 * @Description
 * @create 2025-01-07 14:05
 */
public class ListingHubConfig {
  private static final ReloadableProperties reloadableProperties =
      ReloadablePropertiesFactory.createAppReloadProperties("listingHub");


  private static Properties getProperties() {
    return reloadableProperties.getProperties();
  }

  public static String getProperty(String key) {
    return getProperties().getProperty(key, "");
  }

  public static String getProperty(String key, String defaultValue) {
    return getProperties().getProperty(key, defaultValue);
  }

  public static Integer getIntProperty(String key, String defaultValue) {
    return Integer.parseInt(getProperty(key, defaultValue));
  }
}
