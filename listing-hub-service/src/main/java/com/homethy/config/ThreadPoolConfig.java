package com.homethy.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 * @Description
 * @create 2024-11-21 9:53
 */
@Configuration
public class ThreadPoolConfig {
  private static final int CORE_THREAD = 5;

  private static final int MAX_THREAD = 10;

  private static final int QUEUE_SIZE = 25;

  @Bean(name = "asyncExecutor")
  public Executor asyncExecutor() {
    ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
    executor.setCorePoolSize(CORE_THREAD);
    executor.setMaxPoolSize(MAX_THREAD);
    executor.setQueueCapacity(QUEUE_SIZE);
    executor.setThreadNamePrefix("AsyncExecutor-");
    executor.initialize();
    return executor;
  }
}
