package com.homethy.config;

import com.homethy.configcenter.client.HomethyBasicDataSource;

import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

import lombok.extern.slf4j.Slf4j;


@Slf4j
@Configuration
@MapperScan(basePackages = {"com.homethy.dao"},
    sqlSessionFactoryRef = "sqlSessionFactory")
public class DataSourceConfig {

  @Bean(name = "homethyBasicDataSource")
  @Primary
  public DataSource dataSource() throws Exception {
    HomethyBasicDataSource dataSource = new HomethyBasicDataSource("listing");
    dataSource.afterPropertiesSet();
    log.debug(dataSource + dataSource.toString());
    return dataSource;
  }

  @Bean(name = "transactionManager")
  @Primary
  public DataSourceTransactionManager transactionManager(
      @Qualifier("homethyBasicDataSource") DataSource dataSource) {
    return new DataSourceTransactionManager(dataSource);
  }

  @Bean(name = "sqlSessionFactory")
  @Primary
  public SqlSessionFactory sqlSessionFactory(
      @Qualifier("homethyBasicDataSource") DataSource dataSource) throws Exception {
    final SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
    sessionFactory.setDataSource(dataSource);
    sessionFactory.setConfigLocation(new ClassPathResource("mybatis-conf.xml"));
    return sessionFactory.getObject();
  }
}
