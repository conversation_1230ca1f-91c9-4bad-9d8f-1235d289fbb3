package com.homethy.util;

import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;

import java.util.UUID;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class MdcUtil {
  private static String TRACE_ID = "traceId";
  private static String SPAN_ID = "spanId";

  public static void addTraceId() {
    MDC.put(TRACE_ID, getUUid(true));
  }

  public static void addTraceId(String traceId) {
    MDC.put(TRACE_ID, traceId);
  }

  public static void removeTraceId() {
    MDC.remove(TRACE_ID);
  }

  public static String getTraceId() {
    String traceId = MDC.get(TRACE_ID);
    if (StringUtils.isEmpty(traceId)) {
      traceId = getUUid(true);
    }
    return traceId;
  }

  public static void addSpanId() {
    MDC.put(SPAN_ID, getUUid(false));
  }

  public static void addSpanId(String spanId) {
    MDC.put(SPAN_ID, spanId);
  }

  public static void removeSpanId() {
    MDC.remove(SPAN_ID);
  }

  public static String getSpanId() {
    String spanId = MDC.get(SPAN_ID);
    if (StringUtils.isEmpty(spanId)) {
      spanId = getUUid(false);
    }
    return spanId;
  }

  public static String getUUid(boolean traceFlag) {
    UUID uuid = UUID.randomUUID();
    String uniqueId = uuid.toString().replace("-", "");
    if (traceFlag) {
      return uniqueId.substring(0, 16);
    } else {
      return uniqueId.substring(16);
    }
  }

  public static String resetSpanId() {
    String uUid = RandomStringUtils.randomAlphanumeric(10);
    addSpanId(uUid);
    return uUid;
  }

  public static void clearTrace() {
    MDC.remove(TRACE_ID);
    MDC.remove(SPAN_ID);
  }

  public static void setTrace(String traceId) {
    log.debug("set trace:{}", traceId);
    MDC.put(TRACE_ID, traceId);
  }
}
