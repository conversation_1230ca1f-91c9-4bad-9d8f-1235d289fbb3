package com.homethy.util;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.json.JsonReadFeature;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;

import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class JsonUtil {

  private static final ObjectMapper mapper = new ObjectMapper();

  static {
    //Allow special characters
    mapper.configure(JsonReadFeature.ALLOW_UNESCAPED_CONTROL_CHARS.mappedFeature(), true);
    //Allow the serialization
    mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    //No attribute values and not an error
    mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    //Ignore the escape
    mapper.configure(JsonReadFeature.ALLOW_BACKSLASH_ESCAPING_ANY_CHARACTER.mappedFeature(), true);
  }

  /**
   * Ignore the null field
   */
  public static String toJson(Object obj) {
    try {
      return mapper.writeValueAsString(obj);
    } catch (JsonProcessingException e) {
      throw new IllegalArgumentException("Failed to json:" + obj, e);
    }
  }

  public static String toString(Object o) {
    try {
      return mapper.writerWithDefaultPrettyPrinter().writeValueAsString(o);
    } catch (JsonProcessingException e) {
      throw new IllegalArgumentException("format error:" + o, e);
    }
  }

  public static <T> List<T> toList(String jsonText, Class<T> clazz) {
    if (StringUtils.isEmpty(jsonText)) {
      return Collections.emptyList();
    }
    try {
      JavaType jType = mapper.getTypeFactory().constructParametricType(List.class, clazz);
      return mapper.readValue(jsonText, jType);
    } catch (JsonProcessingException ex) {
      throw new IllegalArgumentException("Failed to toList:" + jsonText, ex);
    }
  }

  public static <T> T toBean(String src, Class<T> clazz) {
    try {
      return mapper.readValue(src, clazz);
    } catch (Exception ex) {
      throw new IllegalArgumentException("Failed to toBean:" + src, ex);
    }
  }

  public static <T> T mapToBean(Map<String, Object> src, Class<T> clazz) {
    return mapper.convertValue(src, clazz);
  }

  public static Map<String, Object> toMap(String json) {
    if (StringUtils.isEmpty(json)) {
      return new HashMap<>();
    }
    try {
      return mapper.readValue(json, new TypeReference<Map<String, Object>>() {
      });
    } catch (IOException ex) {
      throw new IllegalArgumentException("Failed to toMap:" + json, ex);
    }
  }

  public static Map<String, Object> safeToMap(String json) {
    if (StringUtils.isEmpty(json)) {
      return new HashMap<>();
    }
    try {
      return mapper.readValue(json, new TypeReference<Map<String, Object>>() {
      });
    } catch (IOException e) {
      log.error(e.getMessage(), e);
      return new HashMap<>();
    }
  }

  /**
   * TypeReference converting json object
   */
  public static <T> T fromJson(String src, TypeReference<T> typeReference) throws JsonProcessingException {
    return mapper.readValue(src, typeReference);
  }

  public static ObjectNode createObjectNode() {
    return mapper.createObjectNode();
  }

  public static ArrayNode createArrayNode() {
    return mapper.createArrayNode();
  }

  public static JsonNode readTree(String json) {
    try {
      return mapper.readTree(json);
    } catch (JsonProcessingException e) {
      log.error(e.getMessage(), e);
      return null;
    }
  }
}
