package com.homethy.util;

import java.util.HashMap;
import java.util.Map;

public class SynonymReplacer {

  private static final Map<String, String> synonymMap = new HashMap<>();

  static {
    synonymMap.put("pth", "path");
    synonymMap.put("cl", "close");
    synonymMap.put("hse", "house");
    synonymMap.put("mnr", "manor");
    synonymMap.put("rm", "room");
    synonymMap.put("pde", "parade");
    synonymMap.put("gdns", "gardens");
    synonymMap.put("ga", "gate");
    synonymMap.put("gn", "green");
    synonymMap.put("smt", "summit");
    synonymMap.put("te", "terrace");
    synonymMap.put("vill", "village");
    synonymMap.put("vly", "valley");
    synonymMap.put("trl", "trail");
    synonymMap.put("drwy", "driveway");
    synonymMap.put("crl", "circle");
    synonymMap.put("csng", "crossing");
    synonymMap.put("fl", "flat");
    synonymMap.put("hts", "heights");
    synonymMap.put("btm", "bottom");
    synonymMap.put("ter", "terrace");
    synonymMap.put("mt", "mount");
    synonymMap.put("avnue", "avenue");
    synonymMap.put("prkwy", "parkway");
    synonymMap.put("wl", "well");
    synonymMap.put("fgr", "forge");
    synonymMap.put("valy", "valley");
    synonymMap.put("comn", "common");
    synonymMap.put("manr", "manor");
    synonymMap.put("cor", "corner");
    synonymMap.put("pkway", "parkway");
    synonymMap.put("gln", "glen");
    synonymMap.put("cts", "courts");
    synonymMap.put("drv", "drive");
    synonymMap.put("rdg", "ridge");
    synonymMap.put("wds", "woods");
    synonymMap.put("hbr", "harbor");
    synonymMap.put("ci", "circle");
    synonymMap.put("ave", "avenue");
    synonymMap.put("brk", "brook");
    synonymMap.put("byp", "bypass");
    synonymMap.put("cswy", "causeway");
    synonymMap.put("brg", "bridge");
    synonymMap.put("cr", "circle");
    synonymMap.put("ct", "court");
    synonymMap.put("ps", "pass");
    synonymMap.put("pt", "point");
    synonymMap.put("frd", "ford");
    synonymMap.put("msn", "mission");
    synonymMap.put("pa", "path");
    synonymMap.put("wy", "way");
    synonymMap.put("holw", "hollow");
    synonymMap.put("grth", "garth");
    synonymMap.put("ctr", "center");
    synonymMap.put("path", "pathway");
    synonymMap.put("mls", "mills");
    synonymMap.put("rdge", "ridge");
    synonymMap.put("bnd", "bend");
    synonymMap.put("fld", "field");
    synonymMap.put("riv", "river");
    synonymMap.put("is", "island");
    synonymMap.put("vl", "ville");
    synonymMap.put("vw", "view");
    synonymMap.put("ave", "avenue");
    synonymMap.put("crcs", "crossing");
    synonymMap.put("drve", "drive");
    synonymMap.put("crse", "course");
    synonymMap.put("grve", "grove");
    synonymMap.put("cmns", "commons");
    synonymMap.put("crst", "crest");
    synonymMap.put("tunl", "tunnel");
    synonymMap.put("grvs", "groves");
    synonymMap.put("knl", "knoll");
    synonymMap.put("pwky", "parkway");
    synonymMap.put("sq", "square");
    synonymMap.put("sw", "southwest");
    synonymMap.put("pkwy", "parkway");
    synonymMap.put("strm", "stream");
    synonymMap.put("ldg", "lodge");
    synonymMap.put("psge", "passage");
    synonymMap.put("ln", "lane");
    synonymMap.put("frst", "forest");
    synonymMap.put("clf", "cliff");
    synonymMap.put("cres", "crescent");
    synonymMap.put("lk", "lake");
    synonymMap.put("clb", "club");
    synonymMap.put("trak", "track");
    synonymMap.put("terr", "terrace");
    synonymMap.put("dwns", "downs");
    synonymMap.put("est", "estates");
    synonymMap.put("xing", "crossing");
    synonymMap.put("for", "ford");
    synonymMap.put("blv", "boulevard");
    synonymMap.put("rd", "road");
    synonymMap.put("ovlk", "overlook");
    synonymMap.put("blf", "bluff");
    synonymMap.put("trls", "trail");
    synonymMap.put("bl", "boulevard");
    synonymMap.put("bn", "bend");
    synonymMap.put("br", "branch");
    synonymMap.put("cors", "corners");
    synonymMap.put("iss", "islands");
    synonymMap.put("wls", "wells");
    synonymMap.put("frk", "fork");
    synonymMap.put("mdw", "meadows");
    synonymMap.put("rch", "reach");
    synonymMap.put("hy", "highway");
    synonymMap.put("hgts", "heights");
    synonymMap.put("pnt", "point");
    synonymMap.put("ht", "heights");
    synonymMap.put("rst", "rest");
    synonymMap.put("sta", "station");
    synonymMap.put("cir", "circle");
    synonymMap.put("brks", "brooke");
    synonymMap.put("str", "street");
    synonymMap.put("sts", "street");
    synonymMap.put("bvd", "boulevard");
    synonymMap.put("driv", "drive");
    synonymMap.put("grns", "green");
    synonymMap.put("flds", "fields");
    synonymMap.put("prt", "port");
    synonymMap.put("st", "street");
    synonymMap.put("grv", "grove");
    synonymMap.put("grn", "green");
    synonymMap.put("flts", "flats");
    synonymMap.put("rds", "roads");
    synonymMap.put("hllw", "hollow");
    synonymMap.put("gtwy", "gateway");
    synonymMap.put("dr", "drive");
    synonymMap.put("chse", "chase");
    synonymMap.put("ext", "extension");
    synonymMap.put("ky", "key");
    synonymMap.put("e", "east");
    synonymMap.put("s", "south");
    synonymMap.put("w", "west");
    synonymMap.put("n", "north");
  }

  public static String replaceSynonyms(String text) {
    for (Map.Entry<String, String> entry : synonymMap.entrySet()) {
      text = text.replaceAll("(?<!\\w)(" + entry.getKey() + ")(?!\\w)", entry.getValue());
    }
    return text;
  }
}
