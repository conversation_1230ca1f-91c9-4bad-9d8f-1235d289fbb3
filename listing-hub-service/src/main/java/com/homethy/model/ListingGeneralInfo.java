package com.homethy.model;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.util.Date;

import cn.hutool.core.date.DatePattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024-10-08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ListingGeneralInfo {
  private BigDecimal entranceFloor;
  private String epcRating;
  @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
  private Date epcExpiryDate;
  private String councilTax;
  /**
   * enum class: FurnishedType
   */
  private Integer furnished;
  /**
   * enum class: TrueOrFalse
   */
  private Integer petAllowed;
}
