package com.homethy.model;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

import cn.hutool.core.date.DatePattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ListingThirdPartyInfo {
  @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
  private Date rightMovePublishDate;
  @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
  private Date rightMoveLastPublishDate;
  private String rightMoveUrl;
  @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
  private Date zooplaPublishDate;
  @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
  private Date zooplaLastPublishDate;
  private String zooplaUrl;
  /**
   * enum class: TrueOrFalse, default 0, unable to be 0 once it is 1
   */
  private int publishFlag;
}
