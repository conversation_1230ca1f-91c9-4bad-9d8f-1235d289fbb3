package com.homethy.model;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

import cn.hutool.core.date.DatePattern;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024-10-16
 */
@Data
public class ListingAccessInfo {

  /**
   * enum class: ShowingInstructionType
   */
  private Integer showingInstruction;

  @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
  private Date showingDate;
}
