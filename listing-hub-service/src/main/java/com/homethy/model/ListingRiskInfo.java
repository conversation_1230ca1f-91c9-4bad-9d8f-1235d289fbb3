package com.homethy.model;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

import cn.hutool.core.date.DatePattern;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024-10-08
 */
@Data
public class ListingRiskInfo {
  /**
   * enum class: TrueOrFalse
   */
  private Integer floodedInLast5Years;

  @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
  private Date lastFloodingDate;
  /**
   * enum class: SourcesOfFloodingType
   */
  private String sourcesOfFlooding;
  /**
   * enum class: TrueOrFalse
   */
  private Integer erosionRisk;
  /**
   * enum class: TrueOrFalse
   */
  private Integer coalfieldImpacted;
}
