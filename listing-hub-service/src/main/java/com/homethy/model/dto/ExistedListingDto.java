package com.homethy.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.homethy.model.ListingSoldHistory;

import java.util.Date;
import java.util.List;

import cn.hutool.core.date.DatePattern;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description
 * @create 2024-10-24 16:25
 */
@Data
public class ExistedListingDto {
  private Long id;
  private Integer marketStatus;
  private Integer newMarketStatus;
  private Integer newListingStatus; // WITHDRAWN, SOLD or Let
  @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
  private Date rightMovePublishDate;
  @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
  private Date zooplaPublishDate;
  private List<ListingSoldHistory> soldHistories;
}
