package com.homethy.model.dto;

import com.homethy.enums.PropertyType;
import com.homethy.microservice.client.model.LeadBo;

import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * <AUTHOR>
 * @Description
 * @create 2025-01-07 10:02
 */
@Data
public class ZooplaApplicantDto {
  private String id;
  private Contact contact;
  private Date createdAt;
  private Intent intent;
  private EnquiryType enquiryType;
  private String message;
  private String propertyUse;
  private boolean isOverseas;
  private String portalLeadId;
  private String portalBranchId;
  private String portalCompanyId;
  private String sourceBranchId;
  // private List<ViewingTime> viewingTimes;
  private boolean organiseViewing;
  private boolean requestPropertyDetails;
  private String consumerSituation;
  private String consumerPostcode;
  private String recipientEmail;
  private String leadSource;
  private String portalSource;
  private SearchCriteria searchCriteria;
  private ListingDetails listingDetails;

  @Data
  public static class Contact {
    private String firstName;
    private String lastName;
    private String phoneNumber;
    private String email;
    /*private Preferences preferences;

    @Data
    public static class Preferences {
      private String method;
      private String time;
    }*/
  }

  /*@Data
  public static class ViewingTime {
    private Date earliest;
    private Date latest;
  }*/

  @Data
  public static class SearchCriteria {
    private Location location;
    private int radius;
    private Integer minimumBedrooms;
    private Integer maximumBedrooms;
    private Double minimumPrice;
    private Double maximumPrice;
    private String priceFrequency;
    private ZooplaPropertyType propertyType;
    private String propertySubType;

    @Data
    public static class Location {
      private String alias;
      private String area;
      private String areaStreet;
      private String country;
      private String countyArea;
      private String incode;
      private String outcode;
      private String postTown;
      private String postalArea;
      private String region;
      private String district;
      private String countryCode;
    }
  }

  @Data
  public static class ListingDetails {
    private String id;
    private Integer bedrooms;
    private Integer bathrooms;
    private Integer receptions;
    private String lifeCycleStatus;
    private Address address;
    private Pricing pricing;
    private String propertyId;
    private ZooplaPropertyType propertyType;
    private String sourceBranchId;
    private String sourceId;
    private String url;
    private List<Image> images;

    @Data
    public static class Address {
      private String propertyNumberOrName;
      private String streetName;
      private String locality;
      private String townOrCity;
      private String postcode;
      private String countryCode;
    }

    @Data
    public static class Pricing {
      private String transactionType;
      private String currencyCode;
      private Double price;
      private String rentFrequency;
    }

    @Data
    public static class Image {
      private String original;
      private String cdnUrl;
      private String filename;
    }
  }

  public enum Intent {
    APPLICANT_INTENT_BUY,
    APPLICANT_INTENT_RENT,
    APPLICANT_INTENT_UNSPECIFIED;

    public static int toCrmLeadType(Intent intent) {
      return switch (intent) {
        case APPLICANT_INTENT_BUY -> LeadBo.LeadListingTypeEnum.Buy.getType();
        case APPLICANT_INTENT_RENT -> LeadBo.LeadListingTypeEnum.Renter.getType();
        case APPLICANT_INTENT_UNSPECIFIED -> LeadBo.LeadListingTypeEnum.Other.getType();
      };
    }
  }

  public enum EnquiryType {
    ENQUIRY_TYPE_SALES,
    ENQUIRY_TYPE_LETTINGS,
    ENQUIRY_TYPE_UNSPECIFIED
  }

  public enum ZooplaPropertyType {
    PROPERTY_TYPE_UNSPECIFIED,
    PROPERTY_TYPE_FLAT,
    PROPERTY_TYPE_DETACHED,
    PROPERTY_TYPE_SEMI_DETACHED,
    PROPERTY_TYPE_TERRACED,
    PROPERTY_TYPE_MID_TERRACE,
    PROPERTY_TYPE_END_TERRACE,
    PROPERTY_TYPE_DETACHED_BUNGALOW,
    PROPERTY_TYPE_BUNGALOW,
    PROPERTY_TYPE_STUDIO,
    PROPERTY_TYPE_MAISONETTE,
    PROPERTY_TYPE_TOWN_HOUSE,
    PROPERTY_TYPE_COTTAGE,
    PROPERTY_TYPE_SEMI_DETACHED_BUNGALOW,
    PROPERTY_TYPE_LAND,
    PROPERTY_TYPE_PARK_HOME,
    PROPERTY_TYPE_LINK_DETACHED,
    PROPERTY_TYPE_BARN_CONVERSION,
    PROPERTY_TYPE_MEWS,
    PROPERTY_TYPE_CHALET,
    PROPERTY_TYPE_VILLA,
    PROPERTY_TYPE_FARMHOUSE,
    PROPERTY_TYPE_COUNTRY_HOUSE,
    PROPERTY_TYPE_TERRACED_BUNGALOW,
    PROPERTY_TYPE_PARKING,
    PROPERTY_TYPE_BLOCK_OF_FLATS,
    PROPERTY_TYPE_EQUESTRIAN,
    PROPERTY_TYPE_LODGE,
    PROPERTY_TYPE_HOUSEBOAT,
    PROPERTY_TYPE_FARM,
    PROPERTY_TYPE_CHATEAU,
    PROPERTY_TYPE_FARM_BARN,
    PROPERTY_TYPE_FINCA,
    PROPERTY_TYPE_HOTEL,
    PROPERTY_TYPE_INDUSTRIAL,
    PROPERTY_TYPE_JUNK,
    PROPERTY_TYPE_LEISURE,
    PROPERTY_TYPE_LIGHT_INDUSTRIAL,
    PROPERTY_TYPE_LONGERE,
    PROPERTY_TYPE_OFFICE,
    PROPERTY_TYPE_PUB_BAR,
    PROPERTY_TYPE_RESTAURANT,
    PROPERTY_TYPE_RETAIL,
    PROPERTY_TYPE_RIAD,
    PROPERTY_TYPE_WAREHOUSE;

    public static String toCrmPropertyType(ZooplaPropertyType type) {
      PropertyType propertyType =  switch (type) {
        case PROPERTY_TYPE_FLAT -> PropertyType.FLAT;
        case PROPERTY_TYPE_DETACHED,
            PROPERTY_TYPE_DETACHED_BUNGALOW,
            PROPERTY_TYPE_LINK_DETACHED -> PropertyType.DETACHED;
        case PROPERTY_TYPE_SEMI_DETACHED,
            PROPERTY_TYPE_SEMI_DETACHED_BUNGALOW -> PropertyType.SEMI_DETACHED;
        case PROPERTY_TYPE_TERRACED,
            PROPERTY_TYPE_MID_TERRACE,
            PROPERTY_TYPE_TERRACED_BUNGALOW -> PropertyType.TERRACED;
        case PROPERTY_TYPE_END_TERRACE -> PropertyType.END_OF_TERRACE;
        case PROPERTY_TYPE_BUNGALOW -> PropertyType.BUNGALOW;
        case PROPERTY_TYPE_STUDIO -> PropertyType.STUDIO;
        case PROPERTY_TYPE_MAISONETTE -> PropertyType.MAISONETTE;
        case PROPERTY_TYPE_TOWN_HOUSE -> PropertyType.TOWN_HOUSE;
        case PROPERTY_TYPE_COTTAGE -> PropertyType.COTTAGE;
        case PROPERTY_TYPE_LAND -> PropertyType.LAND;
        case PROPERTY_TYPE_PARK_HOME -> PropertyType.MOBILE_STATIC;
        case PROPERTY_TYPE_BARN_CONVERSION,
            PROPERTY_TYPE_FARMHOUSE,
            PROPERTY_TYPE_FARM,
            PROPERTY_TYPE_FARM_BARN -> PropertyType.FARM_BARN;
        case PROPERTY_TYPE_MEWS -> PropertyType.MEWS_HOUSE;
        case PROPERTY_TYPE_BLOCK_OF_FLATS -> PropertyType.BLOCK_OF_FLATS;
        case PROPERTY_TYPE_OFFICE -> PropertyType.OFFICE;
        default -> PropertyType.OTHER;
      };
      return propertyType.getDescription();
    }
  }

}
