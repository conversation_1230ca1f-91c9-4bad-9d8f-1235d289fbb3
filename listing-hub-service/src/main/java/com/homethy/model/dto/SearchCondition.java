package com.homethy.model.dto;

import com.homethy.util.JsonUtil;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/11/28 17:44
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Slf4j
public class SearchCondition {
  private long siteId;

  private long userId;
  private long teamId;
  private int page;
  private int size;
  private String listingSort;
  private long sheetId;
  private int listingManagementType;
  private String condition;
  private String newListingUseMlsAgent;
  private List<Long> officeIds;
  private List<Long> agentIds;
  private List<Long> stageIds;
  private Map<String, Object> params;
  private boolean enterprise;
  private boolean forced;
  private Integer useSelectedSite;
  private int crmTimeZone;
  private int siteLevel;
  private boolean soldFlag;
  private String name;
  private List<Long> listingIds;
  private List<Long> notListingIds;
  private String returnFieldsStr;
  private boolean notCreateOpenHouse;
  private boolean notCreateTextCode;
  private String agentUserIds;
  private ListingSearchCondition listingSearchCondition;

  /**
   * True Search map False Search list
   */
  private boolean mapSearch;
  private boolean participantOnly;
  private boolean includePermissions;
  private String rect;
  private String threshold;
  private Integer zoom;
  private String mapPath;
  private String mapRadius;

  // Following are some parameters added by sold
  private static final String EMPTY = "";
  @Builder.Default
  private String longitude = EMPTY;
  @Builder.Default
  private String latitude = EMPTY;
  @Builder.Default
  private Integer radius = 27;
  //  private GeoAggregation aggregationType;
  @Builder.Default
  private String layoutType = "";
  //  private String source = SearchSource.SEARCH.getType();
  private String listingStatus;
  private String listingIdsStr;
  private String ids;
  private String featureListingName;
  private String centerPoint;
  private String centerPointCity;
  private String customizedNbrId;
  private String statTypes;
  private String aggType;
  @Builder.Default
  private Integer aggSize = 0;
  private String timezone; // It should be repeated with crmTimezone
  @Builder.Default
  private String key = "";
  @Builder.Default
  private String keywordType = "";
  @Builder.Default
  private boolean ignoreCmsFilter = false;
  @Builder.Default
  private boolean onlyQuery = false;
  @Builder.Default
  private boolean isBreadCrumbs = false;
  @Builder.Default
  private boolean showExpired = false;

  private List<String> displayFieldNames;

//  private ListingAvgTypeExcludeInvalid[] avgTypes;

  public Map<String, Object> getParams() {
    if (params != null) {
      return params;
    }
    params = new HashMap<>();
    if (StringUtils.isNotBlank(newListingUseMlsAgent)) {
      params.put("newListingUseMlsAgent", newListingUseMlsAgent);
    }
    params.put("listingManagementType", listingManagementType);
    if (showExpired) {
      params.put("isExpired", true);
    }
    if (StringUtils.isNotBlank(condition)) {
      Map<String, Object> map = JsonUtil.toMap(condition);
      map.remove("mapPath");
      map.remove("mapRadius");
      params.putAll(map);
    }
    params.put("page", page);
    params.put("pageSize", size);
    params.put("size", size);
    params.put("isSold", soldFlag);
    params.put("userId", userId);
    if (StringUtils.isNotBlank(listingSort)) {
      params.put("listingSorts", new String[]{listingSort});
    }
    if (StringUtils.isNotBlank(rect)) {
      params.put("rect", rect);
    }
    if (StringUtils.isNotBlank(mapPath)) {
      params.put("mapPath", mapPath);
    }
    if (StringUtils.isNotBlank(mapRadius)) {
      params.put("mapRadius", mapRadius);
    }
    if (StringUtils.isNotBlank(name)) {
      params.put("name", name);
    }
    if (listingIds != null && !listingIds.isEmpty()) {
      params.put("listingIds", listingIds);
    }
    params.put("mapSearch", mapSearch);
    if (mapSearch) {
      params.put("threshold", threshold);
      params.put("zoom", zoom);
    }
    params.put("participantOnly", participantOnly);
    params.put("includePermissions", includePermissions);
    if (StringUtils.isNotBlank(agentUserIds)) {
      params.put("agentUserIds", agentUserIds);
    }

    if (CollectionUtils.isNotEmpty(notListingIds)) {
      params.put("notListingIds", notListingIds);
    }

    if (StringUtils.isNotEmpty(returnFieldsStr)) {
      params.put("returnFieldsStr", returnFieldsStr);
    }

    params.put("crmTimeZone", crmTimeZone);


    // Added part of Sold
    if (StringUtils.isNotBlank(listingIdsStr)) {
      params.put("listingIds", listingIdsStr);
    }
    if (StringUtils.isNotBlank(key)) {
      params.put("key", key);
      params.put("keywordType", keywordType);
    }
    if (StringUtils.isNotBlank(listingStatus)) {
      params.put("listingStatus", listingStatus);
    }
    if (StringUtils.isNotBlank(statTypes)) {
      params.put("statTypes", statTypes);
    }
    if (StringUtils.isNotBlank(aggType)) {
      params.put("aggType", aggType);
      params.put("aggSize", aggSize);
    }
    params.put("isBreadCrumbs", isBreadCrumbs);

    return params;
  }
}
