package com.homethy.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.homethy.model.ListingSoldHistory;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import cn.hutool.core.date.DatePattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/10/30 18:16
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CheckRepeatPropertyDto {
  private Long id;
  private String mlsOrgId;
  private String agentOrganizationId;
  private String duplicateKey1;
  private String streetAddress1;
  private String streetAddress2;
  private String zipCode;
  private String city;
  private String county;
  private String country;
  private String longitude;
  private String latitude;
  private BigDecimal price;
  /**
   * enum class: PurchaseType
   */
  private Integer purchaseType;
  /**
   * enum class: PropertyType
   */
  private Integer propertyType;
  /**
   * enum class: ListingStatus
   */
  private Integer listingStatus; // propertyStatus

  private String primaryAgentName;
  private String primaryAgentId;
  private String agentName1;
  private String agentId1;
  private String agentName2;
  private String agentId2;
  private String agentName3;
  private String agentId3;

  private String bedrooms;
  private String bathrooms;
  private String receptionRooms;
  /**
   * enum class: MarketStatus
   */
  private Integer marketStatus;
  private BigDecimal askPrice;
  @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
  private Date rightMovePublishDate;
  @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
  private Date zooplaPublishDate;
  private List<ListingSoldHistory> soldHistories;

  /**
   * enum class: TrueOrFalse, default 0
   */
  private int deleteFlag;
  /**
   * enum class: TrueOrFalse, default 1
   */
  private int draftFlag;
}
