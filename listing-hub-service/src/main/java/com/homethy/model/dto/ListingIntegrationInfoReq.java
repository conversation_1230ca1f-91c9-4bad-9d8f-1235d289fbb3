package com.homethy.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.homethy.enums.IntegrationType;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description
 * @create 2024-10-31 13:52
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ListingIntegrationInfoReq {
  private Long id;
  private String branchId;
  @NotNull(message = "IntegrationType must not be null")
  private IntegrationType type;
  private Boolean sendWelcomeEmail;
  private Long sourceId;
  // The officeId managed by the current user, not current user's officeId
  @NotNull(message = "OfficeId must not be null")
  private Long officeId;

  // set by BE, from current user
  @JsonIgnore
  private Long agentId;
  @JsonIgnore
  private Long teamId;
}
