package com.homethy.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.homethy.util.CommonUtil;

import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * <AUTHOR>
 * @Description
 * @create 2025-01-09 13:41
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RightmoveApplicantDto {

  @JsonProperty("request_id")
  private String requestId;

  private String message;

  private boolean success;

  @JsonProperty("request_timestamp")
  @JsonFormat(pattern = CommonUtil.UK_DATETIME_PATTERN)
  private Date requestTimestamp;

  @JsonProperty("response_timestamp")
  @JsonFormat(pattern = CommonUtil.UK_DATETIME_PATTERN)
  private Date responseTimestamp;

  @JsonProperty("replication_lag")
  private Integer replicationLag;

  @JsonProperty("export_period")
  private ExportPeriod exportPeriod;

  private Branch branch;

  private List<Email> emails;

  private List<RightmoveError> errors;

  @Data
  public static class ExportPeriod {
    @JsonProperty("start_date_time")
    @JsonFormat(pattern = CommonUtil.UK_DATETIME_PATTERN)
    private Date startDateTime;

    @JsonProperty("end_date_time")
    @JsonFormat(pattern = CommonUtil.UK_DATETIME_PATTERN)
    private Date endDateTime;
  }

  @Data
  public static class Branch {
    @JsonProperty("branch_id")
    private int branchId;
  }

  @Data
  public static class RightmoveError {
    @JsonProperty("error_code")
    private String errorCode;
    @JsonProperty("error_value")
    private String errorValue;
    @JsonProperty("error_description")
    private String errorDescription;
  }



  @Data
  public static class Email {
    @JsonProperty("email_id")
    private int emailId;

    @JsonProperty("from_address")
    private String fromAddress;

    @JsonProperty("to_address")
    private String toAddress;

    @JsonProperty("email_date")
    @JsonFormat(pattern = CommonUtil.UK_DATETIME_PATTERN)
    private Date emailDate;

    @JsonProperty("email_types")
    private List<Integer> emailTypes;

    private User user;

    private Property property;

    @Data
    public static class User {
      @JsonProperty("user_contact_details")
      private UserContactDetails userContactDetails;

      @JsonProperty("user_information")
      private UserInformation userInformation;

      @Data
      public static class UserContactDetails {
        private String title;

        @JsonProperty("first_name")
        private String firstName;

        @JsonProperty("last_name")
        private String lastName;

        private String address;

        private String postcode;

        private String country;

        @JsonProperty("phone_day")
        private String phoneDay;

        @JsonProperty("phone_evening")
        private String phoneEvening;

        @JsonProperty("dpa_flag")
        private Boolean dpaFlag;
      }

      @Data
      public static class UserInformation {
        @JsonProperty("move_date")
        private Integer moveDate;

        @JsonProperty("moving_reason")
        private String movingReason;

        @JsonProperty("property_to_sell")
        private Integer propertyToSell;

        @JsonProperty("property_to_rent")
        private Integer propertyToRent;

        @JsonProperty("financial_advice")
        private Boolean financialAdvice;

        @JsonProperty("part_exchange")
        private Boolean partExchange;

        private String comments;

        @JsonProperty("renter_application_url")
        private String renterApplicationUrl;
      }
    }

    @Data
    public static class Property {
      @JsonProperty("agent_ref")
      private String agentRef;

      @JsonProperty("rightmove_id")
      private Integer rightmoveId;

      @JsonProperty("rightmove_url")
      private String rightmoveUrl;

      private Integer price;

      private String postcode;

      private Integer bedrooms;

      private String style;

      @JsonProperty("property_type")
      private int propertyType;
    }
  }
}