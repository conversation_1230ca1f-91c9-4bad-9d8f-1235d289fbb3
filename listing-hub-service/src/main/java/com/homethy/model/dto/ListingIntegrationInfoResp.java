package com.homethy.model.dto;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.homethy.enums.IntegrationType;

import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description
 * @create 2025-01-14 10:39
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ListingIntegrationInfoResp {
  private long id;
  private IntegrationType type;
  @JsonAnyGetter
  @JsonIgnore
  private Map<String, Object> multiFields;
}
