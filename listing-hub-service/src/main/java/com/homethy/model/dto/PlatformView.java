package com.homethy.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description
 * @create 2024-11-26 10:02
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PlatformView {
  // RIGHT_MOVE, WEBSITE
  private String platformName;
  private long today;
  private long last7Days;
  private long last14Days;
  // for rightMove, total is the last28Days
  private long total;
}
