package com.homethy.model.zoopla;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description
 * @create 2024-11-05 17:25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ZooplaPublishResp {
  private String status;
  @JsonProperty("listing_reference")
  private String listingReference;
  @JsonProperty("listing_etag")
  private String listingEtag;
  private String url;
  @JsonProperty("new_listing")
  private boolean newListing;
}
