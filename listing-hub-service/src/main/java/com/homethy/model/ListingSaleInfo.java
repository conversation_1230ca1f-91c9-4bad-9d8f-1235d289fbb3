package com.homethy.model;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.util.Date;

import cn.hutool.core.date.DatePattern;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024-10-08
 */
@Data
public class ListingSaleInfo {
  /**
   * enum class: TenureType
   */
  private Integer tenure;
  @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
  private Date tenureExpiryDate;
  private BigDecimal annualServiceCharge;
  @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
  private Date serviceChargeReviewDate;
  private BigDecimal annualGroundRent;
  @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
  private Date groundReviewDate;
  private BigDecimal groundRentReviewFrequency;
  private BigDecimal groundRentIncrease;
  private String priceAndChargesDetails;
  private BigDecimal ownershipShared;
  private BigDecimal ownershipRent;
  /**
   * enum class: RentFrequencyType
   */
  private Integer rentFrequency;
  private BigDecimal commonholdAssessment;
  /**
   * enum class: RentalFrequencyType
   */
  private Integer rentalFrequency;
  @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
  private Date availableFrom;
  @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
  private Date availableTo;
  private BigDecimal deposit;
}
