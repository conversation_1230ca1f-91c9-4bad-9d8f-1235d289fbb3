package com.homethy.model;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024-10-08
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ListingMultiFieldsUK {
  private ListingGeneralInfo generalInfo;
  private ListingSaleInfo saleInfo;
  private ListingUtilityInfo utilityInfo;
  private ListingRightAndRestriction rightAndRestriction;
  private ListingRiskInfo riskInfo;
  private ListingAccessInfo accessInfo;
  private ListingThirdPartyInfo thirdPartyInfo;
  private ListingChimeFields chimeFields;
  private List<ListingSoldHistory> soldHistories;
}
