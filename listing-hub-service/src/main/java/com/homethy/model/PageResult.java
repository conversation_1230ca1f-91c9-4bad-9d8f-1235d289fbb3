package com.homethy.model;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @create 2024-10-15 15:10
 */
public class PageResult<T> {

  //current page
  private int pageNum;

  private int pageSize;

  private int totalPage;

  //total
  private int count;

  private List<T> pageData;

  /**
   * @param pageNum
   * @param pageSize
   * @param data     all data before pagination.
   */
  public PageResult(int pageNum, int pageSize, List<T> data) {
    this.pageNum = Math.max(pageNum, 1);
    this.pageSize = Math.max(pageSize, 1);
    this.count = (data != null) ? data.size() : 0;
    this.totalPage = this.getTotalPage();
    this.pageData = this.pager(data);
  }

  /**
   * @param pageNum
   * @param pageSize
   * @param currentPageDate     data on the current page.
   * @param count    total count of all records.
   */
  public PageResult(int pageNum, int pageSize, List<T> currentPageDate, int count) {
    this.pageNum = Math.max(pageNum, 1);
    this.pageSize = Math.max(pageSize, 1);
    this.count = count;
    this.totalPage = this.getTotalPage();
    this.pageData = currentPageDate;
  }

  private int getTotalPage() {
    if (this.count < 1) {
      return 0;
    }
    return (this.count + this.pageSize - 1) / this.pageSize;
  }


  private List<T> pager(List<T> list) {
    //no data
    if (list == null || list.isEmpty() || this.pageNum > this.totalPage) {
      return List.of();
    }
    int start = (this.pageNum - 1) * this.pageSize;
    int end = Math.min(start + this.pageSize, this.count);
    return list.subList(start, end);
  }
}