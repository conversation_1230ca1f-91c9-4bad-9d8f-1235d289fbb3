package com.homethy.model;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024-10-08
 */
@Data
public class ListingRightAndRestriction {
  /**
   * enum class: TrueOrFalse
   */
  private Integer conservationArea;
  /**
   * enum class: TrueOrFalse
   */
  private Integer leaseRestrictions;
  /**
   * enum class: TrueOrFalse
   */
  private Integer listedBuilding;
  /**
   * enum class: TrueOrFalse
   */
  private Integer permittedDevelopment;
  /**
   * enum class: TrueOrFalse
   */
  private Integer restrictiveCovenant;
  /**
   * enum class: TrueOrFalse
   */
  private Integer propertySubletting;
  /**
   * enum class: TrueOrFalse
   */
  private Integer treePreservationOrder;
}
