package com.homethy.model;

import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/11/4 13:58
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ListingThirdPartyViewInfo {
  private Long id;
  private String teamId;               // team id,  company id
  private String agentOrganizationId;  // office id
  private Long propertyId;
  private Integer integrationType;     // IntegrationType, 1: RightMove, 2: Zoopla
  private String multiFields;          // integration content
  private Date createTime;
  private String createUser;
}
