package com.homethy.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.homethy.enums.ListingStatus;
import com.homethy.util.CommonUtil;

import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.regex.Matcher;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import cn.hutool.core.date.DatePattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024-09-29
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ListingInfo {
  private Long id;
  private String mlsOrgId; // companyId
  private String agentOrganizationId; // officeId
  private String agentOrganizationName; // officeName
  private String duplicateKey1;

  private String streetAddress1;
  private String streetAddress2;
  private String zipCode; // postCode
  private String city; // town
  private String county; // state
  private String country;
  private String longitude;
  private String latitude;

  private BigDecimal price;
  private BigDecimal soldPrice;
  @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
  private Date soldDate;

  /**
   * enum class: PurchaseType
   */
  private Integer purchaseType;
  /**
   * enum class: PropertyType
   */
  private Integer propertyType;
  /**
   * enum class: ListingStatus
   */
  private Integer listingStatus; // propertyStatus

  private String primaryAgentName;
  private String primaryAgentId;
  private String agentName1;
  private String agentId1;
  private String agentName2;
  private String agentId2;
  private String agentName3;
  private String agentId3;

  private String bedrooms;
  private String bathrooms;
  private String receptionRooms;
  private BigDecimal stories; // totalFloor
  private BigDecimal totalBuildingSqm;
  private BigDecimal totalBuildingSqft;
  private Integer builtYear;
  /**
   * enum class: TrueOrFalse
   */
  private Integer newBuilt;
  /**
   * enum class: PropertyConstructionType
   */
  private Integer propertyConstruction;
  private String detailsDescribe;

  private String openHouseSchedules;
  /**
   * enum class: TrueOrFalse
   */
  private Integer openHouseFlag;
  private String openHouseInfo;

  private String listingPicturesWithType;
  private String video;
  private String virtualTour;

  /**
   * class: ListingMultiFieldsUK
   */
  private String multiFields;
  /**
   * enum class: MarketStatus
   */
  private Integer marketStatus;
  @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
  private Date createTime;
  private String createUser;
  private String createUserName;
  @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
  private Date updateTime;
  private String updateUser;
  private String updateUserName;

  /**
   * enum class: TrueOrFalse, default 0
   */
  private int deleteFlag;
  /**
   * enum class: TrueOrFalse, default 1
   */
  private int draftFlag;
  /**
   * enum class: TrueOrFalse, default 1
   */
  private Integer displayOnInternet;

  public Integer parseBedrooms() {
    return this.parseListingRooms(this.bedrooms);
  }

  public Integer parseBathrooms() {
    return this.parseListingRooms(this.bathrooms);
  }

  public Integer parseReceptionRooms() {
    return this.parseListingRooms(this.receptionRooms);
  }

  private Integer parseListingRooms(String rooms) {
    Matcher matcher = StringUtils.isNotEmpty(rooms) ?
        CommonUtil.LISTING_ROOMS_PATTERN.matcher(rooms) : null;
    if (matcher != null && matcher.find()) {
      return Integer.parseInt(matcher.group());
    }
    return null;
  }

  public BigDecimal getActualPrice() {
    return ListingStatus.SOLD.getCode()==this.getListingStatus() || ListingStatus.LET.getCode()==this.getListingStatus()?
            this.getSoldPrice():
            this.getPrice();
  }

  /**
   * streetAddress2 -> unit
   * @return full address not include streetAddress2
   */
  public String getFullListingAddress() {
    return Stream.of(streetAddress1, city, county, zipCode)
        .filter(StringUtils::isNotBlank).collect(Collectors.joining(", "));
  }

  public String getFullListingAddressWithAddress2() {
    return Stream.of(streetAddress1, streetAddress2, city, county, zipCode)
        .filter(StringUtils::isNotBlank).collect(Collectors.joining(", "));
  }
}
