package com.homethy.model;

import com.homethy.microservice.client.model.LeadBo;
import com.homethy.microservice.client.model.OwnershipBeanBo;
import com.homethy.util.JsonUtil;

import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import static com.homethy.enums.IntegrationType.RIGHT_MOVE;
import static com.homethy.enums.IntegrationType.ZOOPLA;

/**
 * <AUTHOR>
 * @Description
 * @create 2024-10-29 15:23
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
@Slf4j
public class ListingIntegrationInfo {
  private Long id;
  private String teamId;               // team id,  company id
  private String agentOrganizationId;  // office id
  private Integer integrationType;     // IntegrationType, 1: RightMove, 2: Zoopla
  private String multiFields;          // integration content
  private Date createTime;
  private String createUser;
  private Date updateTime;
  private String updateUser;

  public String getBranchId() {
    try {
      if (integrationType == ZOOPLA.getCode()) {
        return JsonUtil.toBean(multiFields, ZooplaInfo.class).getBranchId();
      } else if (integrationType == RIGHT_MOVE.getCode()) {
        return JsonUtil.toBean(multiFields, RightMoveInfo.class).getBranchId();
      } else {
        return null;
      }
    } catch (Exception e) {
      log.error("Error parsing multiFields: {}", e.getMessage());
      return null;
    }
  }

  public int getSourceId() {
    if (integrationType == ZOOPLA.getCode()) {
      return JsonUtil.toBean(multiFields, ZooplaInfo.class).getSourceId();
    } else if (integrationType == RIGHT_MOVE.getCode()) {
      return JsonUtil.toBean(multiFields, RightMoveInfo.class).getSourceId();
    } else {
      throw new IllegalArgumentException("Invalid integration type");
    }
  }

  public boolean isSendWelcomeEmail() {
    if (integrationType == ZOOPLA.getCode()) {
      return JsonUtil.toBean(multiFields, ZooplaInfo.class).isSendWelcomeEmail();
    } else if (integrationType == RIGHT_MOVE.getCode()) {
      return JsonUtil.toBean(multiFields, RightMoveInfo.class).isSendWelcomeEmail();
    } else {
      throw new IllegalArgumentException("Invalid integration type");
    }
  }

  /**
   * zoopla/rightmove only have office/company dimensions
   * company: ownershipId -> teamId
   * office: ownershipId -> officeId
   *
   * @return zoopla/rightmove ownership
   */
  public OwnershipBeanBo getOwnership() {
    if (this.integrationType == ZOOPLA.getCode() || this.integrationType == RIGHT_MOVE.getCode()) {
      OwnershipBeanBo ownership = new OwnershipBeanBo();
      if ("0".equals(this.agentOrganizationId)) {
        // company
        ownership.setOwnershipScope(LeadBo.OwnershipScopeEnum.TEAM);
        ownership.setOwnershipId(Long.parseLong(this.teamId));
      } else {
        // office
        ownership.setOwnershipScope(LeadBo.OwnershipScopeEnum.OFFICE);
        ownership.setOwnershipId(Long.parseLong(this.agentOrganizationId));
      }
      return ownership;
    }
    return null;
  }
}
