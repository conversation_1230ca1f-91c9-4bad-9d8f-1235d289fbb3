package com.homethy.model;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.util.Date;

import cn.hutool.core.date.DatePattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description
 * @create 2024-10-28 16:24
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ListingSoldHistory {
  @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
  private Date soldDate;
  private BigDecimal soldPrice;
  // purchaseType 0: sale 1: let
  private Integer type;
}
