package com.homethy.model;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

import cn.hutool.core.date.DatePattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/11/4 14:41
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ListingChimeFields {
  /**
   * enum class: ListingDisplayType
   */
  private String chimeListingDisplay;

  @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
  private Date onMyWebsiteDate;
  @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
  private Date onTeamWebsiteDate;
}
