package com.homethy.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.homethy.enums.ListingStatus;

import java.util.Date;

import cn.hutool.core.date.DatePattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024-10-08
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ListingUpdateInfo {
  private Long id;
  private Long mlsOrgId;
  private String mlsListingId;
  /**
   * enum class: ListingUpdateType, 1新增房源 2价格改变 3状态改变
   */
  private Integer updateType;
  private String newValue;
  private String curPrice;
  private ListingStatus curStatus;
  @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
  private Date createTime;
}
