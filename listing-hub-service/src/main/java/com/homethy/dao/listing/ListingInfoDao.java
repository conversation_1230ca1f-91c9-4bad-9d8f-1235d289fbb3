package com.homethy.dao.listing;

import com.homethy.model.ListingInfo;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Description
 * @create 2024-10-08 10:18
 */
public interface ListingInfoDao {
  String TABLE = " listing_info ";

  String COL_ALL = " id, street_address_1, street_address_2, zip_code, city, county, country, "
      + "longitude, latitude, price, sold_price, sold_date, purchase_type, property_type, "
      + "listing_status, agent_name_1, agent_id_1, agent_name_2, agent_id_2, agent_name_3, "
      + "agent_id_3, primary_agent_name, primary_agent_id, bedrooms, bathrooms, receptionrooms, "
      + "stories, total_building_sqm, total_building_sqft, built_year, new_built, "
      + "property_construction, details_describe, open_house_schedules, open_house_flag, "
      + "open_house_info, listing_pictures_with_type, video, virtual_tour, multi_fields, "
      + "market_status, create_time, create_user, update_time, update_user, delete_flag, "
      + "display_on_internet, mls_org_id, agent_organization_id, duplicate_key1, create_user_name, "
      + "update_user_name, draft_flag, agent_organization_name";

  @Options(useGeneratedKeys = true, keyProperty = "id")
  @Insert("insert into " + TABLE + " set "
      + "street_address_1=#{streetAddress1},"
      + "street_address_2=#{streetAddress2},"
      + "zip_code=#{zipCode},"
      + "city=#{city},"
      + "county=#{county},"
      + "country=#{country},"
      + "longitude=#{longitude},"
      + "latitude=#{latitude},"
      + "price=#{price},"
      + "sold_price=#{soldPrice},"
      + "sold_date=#{soldDate},"
      + "purchase_type=#{purchaseType},"
      + "property_type=#{propertyType},"
      + "listing_status=#{listingStatus},"
      + "primary_agent_name=#{primaryAgentName},"
      + "primary_agent_id=#{primaryAgentId},"
      + "agent_name_1=#{agentName1},"
      + "agent_id_1=#{agentId1},"
      + "agent_name_2=#{agentName2},"
      + "agent_id_2=#{agentId2},"
      + "agent_name_3=#{agentName3},"
      + "agent_id_3=#{agentId3},"
      + "bedrooms=#{bedrooms},"
      + "bathrooms=#{bathrooms},"
      + "receptionrooms=#{receptionRooms},"
      + "stories=#{stories},"
      + "total_building_sqm=#{totalBuildingSqm},"
      + "total_building_sqft=#{totalBuildingSqft},"
      + "built_year=#{builtYear},"
      + "new_built=#{newBuilt},"
      + "property_construction=#{propertyConstruction},"
      + "details_describe=#{detailsDescribe},"
      + "open_house_schedules=#{openHouseSchedules},"
      + "open_house_flag=#{openHouseFlag},"
      + "open_house_info=#{openHouseInfo},"
      + "listing_pictures_with_type=#{listingPicturesWithType},"
      + "video=#{video},"
      + "virtual_tour=#{virtualTour},"
      + "multi_fields=#{multiFields},"
      + "market_status=#{marketStatus},"
      + "create_user=#{createUser},"
      + "update_user=#{updateUser},"
      + "delete_flag=#{deleteFlag},"
      + "draft_flag=#{draftFlag},"
      + "display_on_internet=#{displayOnInternet},"
      + "mls_org_id=#{mlsOrgId},"
      + "agent_organization_id=#{agentOrganizationId},"
      + "agent_organization_name=#{agentOrganizationName},"
      + "duplicate_key1=#{duplicateKey1},"
      + "create_user_name=#{createUserName},"
      + "update_user_name=#{updateUserName}"
  )
  int insertListingInfo(ListingInfo listingInfo);

  @Select("SELECT " + COL_ALL + " FROM " + TABLE + " WHERE id = #{id} "
      + "AND market_status = #{marketStatus} "
      + "AND delete_flag = 0")
  ListingInfo findListingInfoByPrimaryKey(@Param("id") Long id,
                                          @Param("marketStatus") Integer marketStatus);

  @Select("SELECT " + COL_ALL + " FROM " + TABLE + " WHERE id = #{id} AND delete_flag = 0 LIMIT 1")
  Optional<ListingInfo> findListingInfoById(@Param("id") Long id);

  @Update("update " + TABLE + " set "
      + "street_address_1=#{streetAddress1},"
      + "street_address_2=#{streetAddress2},"
      + "zip_code=#{zipCode},"
      + "city=#{city},"
      + "county=#{county},"
      + "country=#{country},"
      + "longitude=#{longitude},"
      + "latitude=#{latitude},"
      + "price=#{price},"
      + "sold_price=#{soldPrice},"
      + "sold_date=#{soldDate},"
      + "purchase_type=#{purchaseType},"
      + "property_type=#{propertyType},"
      + "listing_status=#{listingStatus},"
      + "primary_agent_name=#{primaryAgentName},"
      + "primary_agent_id=#{primaryAgentId},"
      + "agent_name_1=#{agentName1},"
      + "agent_id_1=#{agentId1},"
      + "agent_name_2=#{agentName2},"
      + "agent_id_2=#{agentId2},"
      + "agent_name_3=#{agentName3},"
      + "agent_id_3=#{agentId3},"
      + "bedrooms=#{bedrooms},"
      + "bathrooms=#{bathrooms},"
      + "receptionrooms=#{receptionRooms},"
      + "stories=#{stories},"
      + "total_building_sqm=#{totalBuildingSqm},"
      + "total_building_sqft=#{totalBuildingSqft},"
      + "built_year=#{builtYear},"
      + "new_built=#{newBuilt},"
      + "property_construction=#{propertyConstruction},"
      + "details_describe=#{detailsDescribe},"
      + "open_house_schedules=#{openHouseSchedules},"
      + "open_house_flag=#{openHouseFlag},"
      + "open_house_info=#{openHouseInfo},"
      + "listing_pictures_with_type=#{listingPicturesWithType},"
      + "video=#{video},"
      + "virtual_tour=#{virtualTour},"
      + "multi_fields=#{multiFields},"
      + "market_status=#{marketStatus},"
      + "update_time=#{updateTime},"
      + "update_user=#{updateUser},"
      + "delete_flag=#{deleteFlag},"
      + "draft_flag=#{draftFlag},"
      + "display_on_internet=#{displayOnInternet},"
      + "mls_org_id=#{mlsOrgId},"
      + "agent_organization_id=#{agentOrganizationId},"
      + "agent_organization_name=#{agentOrganizationName},"
      + "duplicate_key1=#{duplicateKey1},"
      + "create_user_name=#{createUserName},"
      + "update_user_name=#{updateUserName} "
      + "WHERE id=#{id}"
  )
  int updateListingInfoById(ListingInfo listingInfo);

  @Select("<script> "
      + "select " + COL_ALL + " from " + TABLE
      + " <where>"
      + " market_status = 0 "
      + " and mls_org_id = #{mlsOrgId} "
      + " and duplicate_key1 = #{duplicateKey1} "
      + "<if test = 'agentOrganizationId != null'>"
      + " and agent_organization_id = #{agentOrganizationId} "
      + "</if>"
      + "</where>"
      + "</script>")
  List<ListingInfo> findRepeatListingInfo(
      @Param("mlsOrgId") String mlsOrgId,
      @Param("duplicateKey1") String duplicateKey1,
      @Param("agentOrganizationId") String agentOrganizationId);
}
