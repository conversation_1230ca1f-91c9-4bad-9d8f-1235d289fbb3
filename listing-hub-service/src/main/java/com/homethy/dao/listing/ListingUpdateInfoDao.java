package com.homethy.dao.listing;

import com.homethy.model.ListingUpdateInfo;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @create 2024-10-08 14:01
 */
public interface ListingUpdateInfoDao {
  String TABLE = " listing_info_update_v2 ";

  String COL_ALL = " id, mls_org_id, mls_listing_id, update_type, new_value, cur_price, cur_status, "
      + "create_time";

  @Options(useGeneratedKeys = true, keyColumn = "id")
  @Insert("INSERT INTO " + TABLE
      + " SET"
      + " mls_org_id=#{mlsOrgId}, "
      + " update_type=#{updateType}, "
      + " new_value=#{newValue}, "
      + " cur_price=#{curPrice}, "
      + " cur_status=#{curStatus},"
      + " mls_listing_id=#{mlsListingId}")
  int insert(ListingUpdateInfo listingUpdateInfo);


  @Select("SELECT " + COL_ALL + " FROM " + TABLE
      + " WHERE mls_listing_id=#{listingId}"
      + " ORDER BY create_time DESC")
  List<ListingUpdateInfo> getListingUpdateInfosById(@Param("listingId") String listingId);
}
