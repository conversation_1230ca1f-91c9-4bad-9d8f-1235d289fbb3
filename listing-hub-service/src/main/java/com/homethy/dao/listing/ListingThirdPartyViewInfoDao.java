package com.homethy.dao.listing;

import com.homethy.model.ListingThirdPartyViewInfo;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @date 2024/11/4 13:24
 */
public interface ListingThirdPartyViewInfoDao {
  String TABLE = " listing_third_party_view_info ";
  String COL_ALL = " id, team_id, agent_organization_id, property_id, integration_type, "
      + "multi_fields, create_time, create_user ";

  @Options(useGeneratedKeys = true, keyProperty = "id")
  @Insert("INSERT INTO " + TABLE
      + " SET"
      + " team_id=#{teamId},"
      + " agent_organization_id=#{agentOrganizationId},"
      + " property_id=#{propertyId},"
      + " integration_type=#{integrationType},"
      + " multi_fields=#{multiFields},"
      + " create_user=#{createUser}")
  int insert(ListingThirdPartyViewInfo info);

  @Select("select count(*) from " + TABLE + " where property_id = #{propertyId}"
      + " and integration_type = #{integrationType} "
  )
  Long getPropertyTotalView(@Param("propertyId") Long propertyId,
                            @Param("integrationType") Integer integrationType);
}
