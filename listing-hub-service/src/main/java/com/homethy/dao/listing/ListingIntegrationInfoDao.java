package com.homethy.dao.listing;

import com.homethy.model.ListingIntegrationInfo;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @create 2024-10-30 9:57
 */
public interface ListingIntegrationInfoDao {
  String TABLE = " listing_integration_info ";
  String COL_ALL = " id, team_id, agent_organization_id, integration_type, "
      + "multi_fields, create_time, create_user, update_time, update_user ";

  @Options(useGeneratedKeys = true, keyProperty = "id")
  @Insert("INSERT INTO " + TABLE
      + " SET"
      + " team_id=#{teamId},"
      + " agent_organization_id=#{agentOrganizationId},"
      + " integration_type=#{integrationType},"
      + " multi_fields=#{multiFields},"
      + " create_user=#{createUser},"
      + " update_user=#{updateUser}")
  int insert(ListingIntegrationInfo integrationInfo);

  @Select("SELECT " + COL_ALL + " FROM " + TABLE + " WHERE id = #{id}")
  ListingIntegrationInfo findById(@Param("id") long id);


  @Select("SELECT " + COL_ALL + " FROM " + TABLE
      + " WHERE team_id=#{teamId}"
      + " AND agent_organization_id=#{officeId}")
  List<ListingIntegrationInfo> getByTeamAndOffice(@Param("teamId") String teamId,
                                                  @Param("officeId") String officeId);

  @Update("UPDATE " + TABLE + " SET "
      + " team_id=#{teamId},"
      + " agent_organization_id=#{agentOrganizationId},"
      + " integration_type=#{integrationType},"
      + " multi_fields=#{multiFields},"
      + " update_time=#{updateTime},"
      + " update_user=#{updateUser}"
      + " WHERE id=#{id}")
  void updateById(ListingIntegrationInfo integrationInfo);

  @Select("select " + COL_ALL + " from " + TABLE + " where integration_type = #{integrationType}")
  List<ListingIntegrationInfo> getAllBranchId(
      @Param("integrationType") Integer integrationType);

  @Select("SELECT " + COL_ALL + " FROM " + TABLE
      + " WHERE team_id=#{teamId}"
      + " AND agent_organization_id=#{officeId}"
      + " AND integration_type=#{type}")
  ListingIntegrationInfo findBranchIdByTeamAndOffice(@Param("teamId") String teamId,
                                                     @Param("officeId") String officeId,
                                                     @Param("type") int type);
}
