package com.homethy.schedule;

import com.homethy.component.RequestContext;
import com.homethy.dao.listing.ListingIntegrationInfoDao;
import com.homethy.enums.IntegrationType;
import com.homethy.enums.PropertyType;
import com.homethy.manager.UserManager;
import com.homethy.microservice.client.model.LeadAddRequestBo;
import com.homethy.microservice.client.model.LeadBo;
import com.homethy.microservice.client.model.LeadInquiriesBo;
import com.homethy.microservice.client.model.LeadPropertyVoBo;
import com.homethy.microservice.client.model.LocationElemBo;
import com.homethy.microservice.client.model.Pair;
import com.homethy.microservice.client.model.UserPhoneBo;
import com.homethy.model.ListingInfo;
import com.homethy.model.ListingIntegrationInfo;
import com.homethy.model.dto.RightmoveApplicantDto;
import com.homethy.service.ListingHubLeadService;
import com.homethy.service.ListingHubRedisService;
import com.homethy.service.ListingInfoService;
import com.homethy.service.RightMoveService;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2024/10/31 14:23
 */
@Component
public class RightMoveTasks {
  private static final Logger logger = LoggerFactory.getLogger(RightMoveTasks.class);

  private final ListingHubRedisService redisService;

  private final ListingIntegrationInfoDao integrationInfoDao;


  private final RightMoveService rightMoveService;

  private final ListingInfoService listingInfoService;

  private static final Integer RANGE_MINUTES = 60;

  private static final String COMMA_SEPARATOR = ",";

  private final ListingHubLeadService listingHubLeadService;

  private final RequestContext requestContext;

  private final UserManager userManager;

  public RightMoveTasks(ListingHubRedisService redisService,
                        ListingIntegrationInfoDao integrationInfoDao,
                        @Lazy RightMoveService rightMoveService,
                        ListingInfoService listingInfoService,
                        ListingHubLeadService listingHubLeadService,
                        RequestContext requestContext, UserManager userManager) {
    this.redisService = redisService;
    this.integrationInfoDao = integrationInfoDao;
    this.rightMoveService = rightMoveService;
    this.listingInfoService = listingInfoService;
    this.listingHubLeadService = listingHubLeadService;
    this.requestContext = requestContext;
    this.userManager = userManager;
  }

  @Scheduled(cron = "0 0 * * * ?")
  public void getViewRecord() {
    String locKey = "listingHub.rightMove.view.record";
    boolean lockAcquired = false;
    try {
      // 分布式锁，避免定时任务重复触发
      lockAcquired = redisService.lockSchedule(locKey, 30 * 60);
      if (lockAcquired) {
        logger.info("RightMove:getViewRecord lock success");
        int type = IntegrationType.RIGHT_MOVE.getCode();
        List<ListingIntegrationInfo> infoList = integrationInfoDao.getAllBranchId(type);
        if (CollectionUtils.isEmpty(infoList)) {
          return;
        }
        infoList.forEach(this::getViewRecord);
      } else {
        logger.info("RightMove:getViewRecord lock failed");
      }
    } finally {
      if (lockAcquired) {
        redisService.unLockSchedule(locKey);
      }
    }
  }

  private RightmoveApplicantDto getRightMoveBranchEmailsMock(int branchId) {
    RightmoveApplicantDto result = null;
    if (requestContext.getData() instanceof RightmoveApplicantDto applicantDto
        && applicantDto.getBranch().getBranchId() == branchId) {
      result = applicantDto;
    }
    return result;
  }

  private RightmoveApplicantDto getRightmoveApplicant(ListingIntegrationInfo info) {
    String branchIdStr = info.getBranchId();
    if (StringUtils.isBlank(branchIdStr)) {
      return null;
    }
    int branchId = Integer.parseInt(branchIdStr);
    boolean mock = requestContext.isMock();
    RightmoveApplicantDto applicant;
    if (mock) {
      applicant = this.getRightMoveBranchEmailsMock(branchId);
    } else {
      applicant = rightMoveService.getRightMoveBranchEmails(branchId, RANGE_MINUTES);
    }
    if (applicant == null || !applicant.isSuccess()
        || CollectionUtils.isEmpty(applicant.getEmails())) {
      logger.error("RightMove:getViewRecord fail, info.id={}, rightmove applicant={} ",
          info.getId(), applicant);
      return null;
    }
    return applicant;
  }

  private void getViewRecord(ListingIntegrationInfo info) {
    RightmoveApplicantDto applicant = this.getRightmoveApplicant(info);
    if (applicant == null) {
      return;
    }
    List<RightmoveApplicantDto.Email> emails = applicant.getEmails();
    // key: email + agentId, value: rightMoveEmails
    Map<String, List<RightmoveApplicantDto.Email>> leadMap = new HashMap<>();
    // key: propertyId, value: <ListingInfo, String[]{url, moving_reason, comments}>
    Map<Long, Pair<ListingInfo, String[]>> listingInfoMap = new HashMap<>();
    this.buildMergeLeadMaps(emails, listingInfoMap, leadMap, info);

    // map to crmLead
    for (Map.Entry<String, List<RightmoveApplicantDto.Email>> entry : leadMap.entrySet()) {
      String key = entry.getKey();
      long agentId = Long.parseLong(key.split(COMMA_SEPARATOR)[1]);
      List<RightmoveApplicantDto.Email> leadEmails = entry.getValue();
      leadEmails.sort(Comparator.comparing(RightmoveApplicantDto.Email::getEmailDate));
      try {
        this.createCrmLead(info, agentId, leadEmails, listingInfoMap);
      } catch (Exception e) {
        logger.error("RightMove:getViewRecord createCrmLead fail, leadEmails:" + leadEmails, e);
      }
    }
  }

  private void createCrmLead(ListingIntegrationInfo info, long agentId,
                             List<RightmoveApplicantDto.Email> leadEmails,
                             Map<Long, Pair<ListingInfo, String[]>> listingInfoMap) {
    List<ListingInfo> listingInfos = leadEmails.stream()
        .map(k -> listingInfoMap.get(Long.parseLong(k.getProperty().getAgentRef())).getKey())
        .toList();
    String teamId = info.getTeamId();
    String officeId = String.valueOf(userManager.getUserOfficeId(agentId));
    // primary agent integration -> ownership
    ListingIntegrationInfo ownershipIntegrationInfo = integrationInfoDao
        .findBranchIdByTeamAndOffice(teamId, officeId, IntegrationType.ZOOPLA.getCode());
    if (ownershipIntegrationInfo == null) {
      return;
    }
    LeadAddRequestBo leadBo = this.buildLeadAddRequestBo(agentId, info, leadEmails, listingInfos,
        ownershipIntegrationInfo);
    List<LeadPropertyVoBo> propertyVoBos = this.buildListingDetail(listingInfos, listingInfoMap);
    // mailingAddress
    LeadPropertyVoBo mailingAddress = this.buildMailingAddress(leadEmails);
    propertyVoBos.add(mailingAddress);
    listingHubLeadService.thirdPartyCreateLead(leadBo, propertyVoBos, listingInfos,
        listingInfoMap, IntegrationType.RIGHT_MOVE);
  }

  private LeadPropertyVoBo buildMailingAddress(List<RightmoveApplicantDto.Email> leadEmails) {
    LeadPropertyVoBo propertyVoBo = new LeadPropertyVoBo();
    propertyVoBo.setMailAddress(true);
    propertyVoBo.setLabel("Home");
    for (RightmoveApplicantDto.Email email : leadEmails) {
      RightmoveApplicantDto.Email.User.UserContactDetails userDetail =
          email.getUser().getUserContactDetails();
      String address = Stream.of(userDetail.getAddress(), userDetail.getCountry())
          .filter(StringUtils::isNotBlank)
          .collect(Collectors.joining(COMMA_SEPARATOR + StringUtils.SPACE));
      // If there was no such thing before, add it; if there was, keep it unchanged.
      if (StringUtils.isEmpty(propertyVoBo.getStreetAddress())
          && StringUtils.isNotEmpty(address)) {
        propertyVoBo.setStreetAddress(address);
      }
      String postcode = userDetail.getPostcode();
      if (StringUtils.isEmpty(propertyVoBo.getZipcode())
          && StringUtils.isNotEmpty(postcode)) {
        propertyVoBo.setZipcode(postcode);
      }
    }
    return propertyVoBo;
  }

  private LeadAddRequestBo buildLeadAddRequestBo(long agentId, ListingIntegrationInfo info,
                                                 List<RightmoveApplicantDto.Email> leadEmails,
                                                 List<ListingInfo> listingInfos,
                                                 ListingIntegrationInfo ownershipIntegrationInfo) {
    LeadAddRequestBo leadBo = listingHubLeadService.initLeadAddRequestBo(
        agentId, IntegrationType.RIGHT_MOVE, ownershipIntegrationInfo, info);
    Set<Integer> propertyTypes = new HashSet<>();
    // leadInfo
    for (RightmoveApplicantDto.Email email : leadEmails) {
      this.buildLeadInfo(email, leadBo);
      propertyTypes.add(email.getProperty().getPropertyType());
    }
    // searchCriteria
    leadBo.setLeadInquiries(this.buildSearchCriteria(listingInfos, propertyTypes));
    // De-duplicate leadPhones leadType
    // distinct by phone
    leadBo.setUserPhoneModels(leadBo.getUserPhoneModels().stream()
        .filter(p -> StringUtils.isNotBlank(p.getPhone()))
        .filter(distinctByKey(UserPhoneBo::getPhone)).toList());
    leadBo.setLeadTypes(leadBo.getLeadTypes().stream().distinct().toList());
    return leadBo;
  }

  // customer distinct function
  private static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
    Set<Object> seen = ConcurrentHashMap.newKeySet();
    return t -> seen.add(keyExtractor.apply(t));
  }

  private void buildMergeLeadMaps(List<RightmoveApplicantDto.Email> emails, Map<Long,
      Pair<ListingInfo, String[]>> listingInfoMap,
                                  Map<String, List<RightmoveApplicantDto.Email>> leadMap,
                                  ListingIntegrationInfo info) {
    for (RightmoveApplicantDto.Email email : emails) {
      long propertyId = Long.parseLong(email.getProperty().getAgentRef());
      logger.info("RightMove propertyId:{}", propertyId);
      ListingInfo listingInfo = listingInfoService.getListingInfoById(propertyId);
      String userEmail = email.getFromAddress();
      if (listingInfo == null || StringUtils.isEmpty(userEmail)) {
        continue;
      }
      // check listingInfo team office with listingIntegrationInfo
      if (!listingInfo.getMlsOrgId().equals(info.getTeamId())
          || !listingInfo.getAgentOrganizationId().equals(info.getAgentOrganizationId())) {
        continue;
      }
      String rightmoveUrl = email.getProperty().getRightmoveUrl();
      RightmoveApplicantDto.Email.User.UserInformation userInformation =
          email.getUser().getUserInformation();
      String movingReason = userInformation.getMovingReason();
      String comments = userInformation.getComments();
      listingInfoMap.computeIfAbsent(propertyId, k ->
          new Pair<>(listingInfo, new String[]{rightmoveUrl, movingReason, comments})
      );
      String key = String.join(COMMA_SEPARATOR, userEmail, listingInfo.getPrimaryAgentId());
      leadMap.computeIfAbsent(key, v -> new ArrayList<>()).add(email);
    }
  }

  private void buildLeadInfo(RightmoveApplicantDto.Email rightMoveEmail,
                             LeadAddRequestBo leadBo) {
    // contact
    RightmoveApplicantDto.Email.User contact = rightMoveEmail.getUser();
    RightmoveApplicantDto.Email.User.UserContactDetails userDetail =
        contact.getUserContactDetails();
    // name
    if (StringUtils.isBlank(leadBo.getLastName()) && StringUtils.isBlank(leadBo.getFirstName())) {
      leadBo.setFirstName(userDetail.getFirstName());
      leadBo.setLastName(userDetail.getLastName());
    }

    // phone
    List<UserPhoneBo> phones = leadBo.getUserPhoneModels();
    if (StringUtils.isNotEmpty(userDetail.getPhoneDay())) {
      UserPhoneBo phoneBo = new UserPhoneBo();
      phoneBo.setPhone(userDetail.getPhoneDay());
      phoneBo.setDescription("Day");
      phones.add(phoneBo);
    }
    if (StringUtils.isNotEmpty(userDetail.getPhoneEvening())) {
      UserPhoneBo phoneBo = new UserPhoneBo();
      phoneBo.setPhone(userDetail.getPhoneEvening());
      phoneBo.setDescription("Evening");
      phones.add(phoneBo);
    }
    leadBo.setUserPhoneModels(phones);
    // email
    leadBo.setEmail(rightMoveEmail.getFromAddress());

    // leadType
    RightmoveApplicantDto.Email.User.UserInformation userInformation = contact.getUserInformation();
    Integer propertyToRent = userInformation.getPropertyToRent();
    Integer propertyToSell = userInformation.getPropertyToSell();
    int leadType = LeadBo.LeadListingTypeEnum.Other.getType();
    if (propertyToRent != null && propertyToRent != 0) {
      leadType = LeadBo.LeadListingTypeEnum.Renter.getType();
      leadBo.getLeadTypes().add(leadType);
    }
    if (propertyToSell != null && propertyToSell != 0) {
      leadType = LeadBo.LeadListingTypeEnum.Sale.getType();
      leadBo.getLeadTypes().add(leadType);
    }
    if (leadType == LeadBo.LeadListingTypeEnum.Other.getType()) {
      leadBo.getLeadTypes().add(leadType);
    }
  }

  private List<LeadPropertyVoBo> buildListingDetail(List<ListingInfo> listingInfos,
                                                    Map<Long, Pair<ListingInfo, String[]>> listingInfoMap) {
    return listingInfos.stream().map(k -> {
      String[] value = listingInfoMap.get(k.getId()).getValue();
      LeadPropertyVoBo propertyBo = listingHubLeadService.buildListingDetail(value[0], k);
      propertyBo.setMovingReason(value[1]);
      propertyBo.setComments(value[2]);
      return propertyBo;
    }).collect(Collectors.toList());
  }

  private LeadInquiriesBo buildSearchCriteria(List<ListingInfo> listingInfos,
                                              Set<Integer> propertyTypes) {
    LeadInquiriesBo inquiriesBo = new LeadInquiriesBo();
    // address
    List<LocationElemBo> locationList = Stream.of(
        listingInfos.stream().map(ListingInfo::getCity)
            .filter(StringUtils::isNotEmpty).distinct()
            .map(city -> new LocationElemBo(LocationElemBo.TYPECITY, city, null)),
        listingInfos.stream().map(ListingInfo::getCounty)
            .filter(StringUtils::isNotEmpty).distinct()
            .map(county -> new LocationElemBo(LocationElemBo.TYPECOUNTY, county, null)),
        listingInfos.stream().map(ListingInfo::getZipCode)
            .filter(StringUtils::isNotEmpty).distinct()
            .map(zipcode -> new LocationElemBo(LocationElemBo.TYPEZIPCODE, zipcode, null)),
        listingInfos.stream().map(ListingInfo::getStreetAddress1)
            .filter(StringUtils::isNotEmpty).distinct()
            .map(street -> new LocationElemBo(LocationElemBo.TYPESTREETADDRESS, street, null))
    ).flatMap(Function.identity()).toList();
    inquiriesBo.setLocations(locationList);
    // price
    List<Long> priceList =
        listingInfos.stream().map(info -> info.getPrice().longValue()).sorted().toList();
    inquiriesBo.setPriceMax(priceList.get(priceList.size() - 1));
    inquiriesBo.setPriceMin(priceList.get(0));
    // propertyType
    String propertyTypesStr = propertyTypes.stream().distinct()
        .map(i -> PropertyType.rightMoveToPropertyType(i).getDescription())
        .collect(Collectors.joining(COMMA_SEPARATOR));
    inquiriesBo.setPropertyType(propertyTypesStr);

    // bedrooms
    List<Integer> bedrooms =
        listingInfos.stream().map(ListingInfo::parseBedrooms)
            .filter(Objects::nonNull).sorted().toList();
    if (CollectionUtils.isNotEmpty(bedrooms)) {
      inquiriesBo.setBedroomsMin(bedrooms.get(0));
    } else {
      inquiriesBo.setBedroomsMin(-1);
    }
    // bathrooms
    List<Integer> bathrooms =
        listingInfos.stream().map(ListingInfo::parseBathrooms)
            .filter(Objects::nonNull).sorted().toList();
    if (CollectionUtils.isNotEmpty(bathrooms)) {
      inquiriesBo.setBathroomsMin(String.valueOf(bathrooms.get(0)));
    } else {
      inquiriesBo.setBathroomsMin("-1");
    }
    return inquiriesBo;
  }

}