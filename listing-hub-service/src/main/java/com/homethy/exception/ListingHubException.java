package com.homethy.exception;

import com.fasterxml.jackson.databind.node.ObjectNode;

import lombok.Getter;

public class ListingHubException extends Exception {

  @Getter
  private final int errorCode;
  @Getter
  private final String errorMsg;
  @Getter
  private ErrorCodeEnum errorCodeEnum;

  @Getter
  private ObjectNode errorData = null;

  public ListingHubException(int errorCode, String errorMsg) {
    super(getDescription(errorCode, errorMsg));
    this.errorCode = errorCode;
    this.errorMsg = errorMsg;
  }

  public ListingHubException(ErrorCodeEnum e) {
    this(e.getErrorCode(), e.getErrorMsg());
    this.errorCodeEnum = e;
  }

  public ListingHubException(ErrorCodeEnum e, ObjectNode errorData) {
    this(e.getErrorCode(), e.getErrorMsg(), errorData);
    this.errorCodeEnum = e;
  }

  public ListingHubException(int errorCode, String errorMsg, ObjectNode errorData) {
    super(getDescription(errorCode, errorMsg));
    this.errorCode = errorCode;
    this.errorMsg = errorMsg;
    this.errorData = errorData;
  }

  private static String getDescription(int errorCode, String errorMsg) {
    return ListingHubException.class.getSimpleName() + ":errorCode=" + errorCode
        + ",errorMsg=" + errorMsg;
  }

}
