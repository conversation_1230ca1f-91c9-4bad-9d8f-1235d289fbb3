package com.homethy.exception;

public enum ErrorCodeEnum {
  // 0 default error
  OK(0, "Success"),
  UNKNOWN_ERROR(100000, "Server Error"),
  LISTING_DATA_ERROR(100001, "Expected one listing, but find many"),
  LISTING_NOT_EXIST(100002, "Listing data not exist"),

  PARAMS_ERROR(100003, "Params error"),

  ZOOPLA_ERROR(100004, "Zoopla API error"),
  ;

  private int errorCode;
  private String errorMsg;

  private ErrorCodeEnum(int errorCode, String errorMsg) {
    this.errorCode = errorCode;
    this.errorMsg = errorMsg;
  }


  public int getErrorCode() {
    return this.errorCode;
  }

  public String getErrorMsg() {
    return this.errorMsg;
  }

}
