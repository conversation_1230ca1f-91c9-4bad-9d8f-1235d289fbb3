package com.homethy.manager;


import com.google.common.collect.Lists;

import com.homethy.microservice.client.bo.curaytor.CuraytorUserViewBo;
import com.homethy.microservice.client.bo.office.OfficeAgentBo;
import com.homethy.microservice.client.bo.office.OfficeBo;
import com.homethy.microservice.client.bo.p.CrmAgentInfo;
import com.homethy.microservice.client.bo.p.User;
import com.homethy.microservice.client.bo.team.vendor.Vendor;
import com.homethy.microservice.client.bo.whitelabel.BrandInfoBo;
import com.homethy.microservice.client.company.CompanyService;
import com.homethy.microservice.client.curaytor.CuraytorUserService;
import com.homethy.microservice.client.office.OfficeAgentService;
import com.homethy.microservice.client.office.OfficePermissionService;
import com.homethy.microservice.client.office.OfficeService;
import com.homethy.microservice.client.offline.AgentOfflineService;
import com.homethy.microservice.client.user.UserService;
import com.homethy.microservice.client.vendor.VendorService;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class UserManager {

  @Autowired
  private OfficePermissionService officePermissionService;

  @Autowired
  private OfficeService officeService;

  @Autowired
  private OfficeAgentService officeAgentService;

  @Autowired
  private AgentOfflineService agentOfflineService;

  @Autowired
  private CuraytorUserService curaytorUserService;

  @Autowired
  private UserService userService;

  @Autowired
  private VendorService vendorService;

  @Autowired
  private CompanyService companyService;

  public User getUserWithDelete(long id) {
    List<User> users = userService.getUserByIdsWithDelete(Lists.newArrayList(id));
    if (CollectionUtils.isEmpty(users)) {
      log.warn("InvalidGetUser AGENT:{}", id);
    }
    return users.get(0);
  }

  public List<User> getUsers(long teamId) {
    List<Long> teamMemberIds = agentOfflineService.getAgreedTeamMemberUserIds(teamId);
    return agentOfflineService.getUserByIds(teamMemberIds);
  }

  public List<User> getAgentsByIds(List<Long> userIds) {
    try {
      return agentOfflineService.getAgentUserByIds(userIds);
    } catch (Exception e) {
      log.warn("get agents by ids failed.", e);
      return Collections.emptyList();
    }
  }

  public List<Long> getOfficeMembers(User user) {
    OfficeBo officeBo = agentOfflineService.byUser(user.getId());
    if (officeBo == null) {
      log.info("office member empty:{}", user.getId());
      return Collections.singletonList(user.getId());
    }
    return officeService.getUserIds(officeBo.getId());
  }

  public List<Long> getOfficeAdmins(long officeId) {
    try {
      return officeAgentService.getAdminAgentIdsByOfficeId(officeId);
    } catch (Exception e) {
      log.warn("get office admin userIds failed", e);
      return Collections.emptyList();
    }
  }

  public boolean isOfficeAdmin(User agent) {
    return officePermissionService.isOfficeAdminOrOwner(agent.getId());
  }

  public boolean isCompanyOwnerOrAdmin(User operator) {
    return officePermissionService.isCompanyAdmin(operator.getId());
  }

  public long getUserOfficeId(long userId) {
    try {
      OfficeAgentBo officeAgent = officeAgentService.getOfficeAgentByUserId(userId);
      return officeAgent == null ? 0L : officeAgent.getOfficeId();
    } catch (Exception e) {
      log.warn("get user office Id failed.", e);
      return 0L;
    }
  }

  public String getOfficeNameById(long officeId) {
    try {
      List<OfficeBo> offices = officeService.getOfficeByIds(List.of(officeId));
      return offices.isEmpty() ? "" : offices.get(0).getName();
    } catch (Exception e) {
      log.warn("getOfficeNameById failed.", e);
      return "";
    }
  }

  public String getTeamNameById(long teamId, long agentId) {
    try {
      return companyService.getCompanyName(teamId, agentId);
    } catch (Exception e) {
      log.warn("getTeamNameById failed.", e);
      return "";
    }
  }

  public List<Long> getChildrenOfficeIncludeSelf(long officeId) {
    try {
      List<Long> result = officeService.getChildrenOfficeIds(officeId);
      result.add(officeId);
      return result.stream().distinct().collect(Collectors.toList());
    } catch (Exception e) {
      log.warn("getChildrenOffice failed. officeId:{}", officeId, e);
      return Lists.newArrayList();
    }
  }

  public BrandInfoBo getEmailBrandInfo(long teamId) {
    if (teamId <= 0) {
      return null;
    }
    try {
      return agentOfflineService.getEmailBrandInfo(teamId);
    } catch (Exception e) {
      log.warn("get brand info failed", e);
      return null;
    }
  }

  public CuraytorUserViewBo getCuraytorUserView(long id) {
    try {
      return curaytorUserService.getByUserId(id);
    } catch (Exception e) {
      log.warn("getCuraytorUserView.id:{}", id, e);
    }
    return null;
  }

  public Map<Long, User> getAllUserMapByIds(List<Long> userIds) {
    if (CollectionUtils.isEmpty(userIds)) {
      return new HashMap<>();
    }
    try {
      return userService.getAllUserMapByIds(userIds);
    } catch (Exception e) {
      log.warn("getAllUserMapByIds.", e);
    }
    return new HashMap<>();
  }

  public Map<Long, Vendor> getVendorByIds(List<Long> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return new HashMap<>();
    }
    return vendorService.queryVendorByIds(ids).stream()
        .collect(Collectors.toMap(Vendor::getId, Function.identity()));
  }

  public long getGroupId(long userId) {
    try {
      OfficeBo officeBo = officeService.byUser(userId);
      if (officeBo != null) {
        return officeBo.getId();
      }
    } catch (Exception e) {
      log.warn("getGroupId.", e);
    }
    return 0;
  }

  public List<Long> getAgentParentOfficeIds(long userId) {
    try {
      return agentOfflineService.getAgentParentOfficeIds(userId);
    } catch (Exception e) {
      log.warn("getGroupOwner.", e);
    }
    return new ArrayList<>();
  }

  public User getMajorUser(User user) {
    User majorUser = null;
    try {
      majorUser = userService.getMajorUserByBaseId(user.getBaseId());
    } catch (Exception e) {
      log.warn("getMajorUserByBaseId.", e);
    }
    return majorUser == null ? user : majorUser;
  }

  public CrmAgentInfo getCrmAgentInfoById(long userId) {
    return agentOfflineService.getAgentInfoById(userId);
  }

}
