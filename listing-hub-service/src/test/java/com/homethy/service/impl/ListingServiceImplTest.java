package com.homethy.service.impl;

import com.homethy.enums.ListingStatus;
import com.homethy.enums.PropertyType;
import com.homethy.enums.PurchaseType;
import com.homethy.model.ListingInfo;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description
 * @create 2024-10-08 14:45
 */
@SpringBootTest
class ListingServiceImplTest {
  @Autowired
  private ListingServiceImpl listingService;

  @Test
  void saveDB() {
    ListingInfo listingInfo = ListingInfo.builder()
        .streetAddress1("123 Main Street")
        .streetAddress2("Apt 4B")
        .zipCode("10001")
        .city("New York")
        .county("New York County")
        .country("USA")
        .longitude("40.712776")
        .latitude("-74.005974")
        .price(new BigDecimal("500000"))
        .soldPrice(new BigDecimal("450000"))
        .purchaseType(PurchaseType.TO_LET.getCode())
        .propertyType(PropertyType.BLOCK_OF_FLATS.getCode())
        .listingStatus(ListingStatus.COMING_SOON.getCode())
        .primaryAgentName("Jake White")
        .primaryAgentId("primaryAgentId")
        .agentName1("John Doe")
        .agentId1("agentId1")
        .agentName2("Jane Smith")
        .agentId2("agentId2")
        .agentName3("Jim Brown")
        .agentId3("agentId3")
        .bedrooms("3")
        .bathrooms("2")
        .receptionRooms("1")
        .stories(new BigDecimal("2.0"))
        .totalBuildingSqm(new BigDecimal("120.0"))
        .totalBuildingSqft(new BigDecimal("1291.67"))
        .builtYear(2005)
        .newBuilt(0)
        .propertyConstruction(1)
        .detailsDescribe("A beautiful detached house in the heart of the city.")
        .openHouseSchedules("2024-10-20 10:00-14:00")
        .openHouseFlag(1)
        .openHouseInfo("First open house of the month.")
        .listingPicturesWithType("http://example.com/pictures")
        .video("http://example.com/video.mp4")
        .virtualTour("http://example.com/virtualtour")
        .multiFields("Additional custom fields")
        .marketStatus(0)
        .createTime(new Date())
        .createUser("system")
        .updateTime(new Date())
        .updateUser("admin")
        .deleteFlag(0)
        .displayOnInternet(1)
        .build();
    //listingService.saveDB(listingInfo);
  }
}