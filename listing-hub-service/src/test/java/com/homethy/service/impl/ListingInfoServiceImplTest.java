package com.homethy.service.impl;

import com.homethy.client.IndexClient;
import com.homethy.exception.ListingHubException;
import com.homethy.service.ListingInfoService;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;

import lombok.extern.slf4j.Slf4j;

import static org.junit.jupiter.api.Assertions.assertThrows;

/**
 * <AUTHOR>
 * @Description
 * @create 2024-10-22 15:45
 */
@SpringBootTest
@Slf4j
class ListingInfoServiceImplTest {
  @Autowired
  private ListingInfoService listingInfoService;

  @MockBean
  private IndexClient indexClient; // Mock FeignClient

  @Test
  void getListingInfoById() {
    assertThrows(ListingHubException.class,
        () -> listingInfoService.getListingInfoById(Long.MAX_VALUE));
  }
}