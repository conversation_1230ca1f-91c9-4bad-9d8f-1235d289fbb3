package com.homethy.dao.listing;

import com.homethy.client.IndexClient;
import com.homethy.model.ListingInfo;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.util.Optional;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Description
 * @create 2024-10-15 14:35
 */
@SpringBootTest
@Slf4j
class ListingInfoDaoTest {

  @Autowired
  private ListingInfoDao listingInfoDao;

  @MockBean
  private IndexClient indexClient; // Mock FeignClient

  @Test
  void getListingInfosById() {
    Optional<ListingInfo> listingInfo = listingInfoDao.findListingInfoById(100L);
    log.info("res:{}", listingInfo.orElse(null));
  }
}