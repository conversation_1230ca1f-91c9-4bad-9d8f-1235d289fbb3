package com.homethy.enums;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.Test;

public class RightmovePropertyTypeTest{

    @Test
    public void testRightMoveToPropertyTypeMobileStatic() {
        assertEquals(PropertyType.MOBILE_STATIC, PropertyType.rightMoveToPropertyType(16));
        assertEquals(PropertyType.MOBILE_STATIC, PropertyType.rightMoveToPropertyType(50));
        assertEquals(PropertyType.MOBILE_STATIC, PropertyType.rightMoveToPropertyType(117));
    }

    @Test
    public void testRightMoveToPropertyTypeTownHouse() {
        assertEquals(PropertyType.TOWN_HOUSE, PropertyType.rightMoveToPropertyType(22));
    }

    @Test
    public void testRightMoveToPropertyTypeOffice() {
        assertEquals(PropertyType.OFFICE, PropertyType.rightMoveToPropertyType(178));
        assertEquals(PropertyType.OFFICE, PropertyType.rightMoveToPropertyType(184));
    }

    @Test
    public void testRightMoveToPropertyTypeTerraced() {
        assertEquals(PropertyType.TERRACED, PropertyType.rightMoveToPropertyType(1));
        assertEquals(PropertyType.TERRACED, PropertyType.rightMoveToPropertyType(13));
    }

    @Test
    public void testRightMoveToPropertyTypeFlat() {
        assertEquals(PropertyType.FLAT, PropertyType.rightMoveToPropertyType(7));
        assertEquals(PropertyType.FLAT, PropertyType.rightMoveToPropertyType(8));
        assertEquals(PropertyType.FLAT, PropertyType.rightMoveToPropertyType(28));
        assertEquals(PropertyType.FLAT, PropertyType.rightMoveToPropertyType(29));
        assertEquals(PropertyType.FLAT, PropertyType.rightMoveToPropertyType(44));
        assertEquals(PropertyType.FLAT, PropertyType.rightMoveToPropertyType(56));
        assertEquals(PropertyType.FLAT, PropertyType.rightMoveToPropertyType(59));
    }

    @Test
    public void testRightMoveToPropertyTypeBungalow() {
        assertEquals(PropertyType.BUNGALOW, PropertyType.rightMoveToPropertyType(12));
    }

    @Test
    public void testRightMoveToPropertyTypeCottage() {
        assertEquals(PropertyType.COTTAGE, PropertyType.rightMoveToPropertyType(23));
    }

    @Test
    public void testRightMoveToPropertyTypeBlockOfFlats() {
        assertEquals(PropertyType.BLOCK_OF_FLATS, PropertyType.rightMoveToPropertyType(143));
    }

    @Test
    public void testRightMoveToPropertyTypeEndOfTerrace() {
        assertEquals(PropertyType.END_OF_TERRACE, PropertyType.rightMoveToPropertyType(2));
    }

    @Test
    public void testRightMoveToPropertyTypeMewsHouse() {
        assertEquals(PropertyType.MEWS_HOUSE, PropertyType.rightMoveToPropertyType(5));
    }

    @Test
    public void testRightMoveToPropertyTypeStudio() {
        assertEquals(PropertyType.STUDIO, PropertyType.rightMoveToPropertyType(9));
    }

    @Test
    public void testRightMoveToPropertyTypeMaisonette() {
        assertEquals(PropertyType.MAISONETTE, PropertyType.rightMoveToPropertyType(10));
        assertEquals(PropertyType.MAISONETTE, PropertyType.rightMoveToPropertyType(11));
    }

    @Test
    public void testRightMoveToPropertyTypeLand() {
        assertEquals(PropertyType.LAND, PropertyType.rightMoveToPropertyType(20));
        assertEquals(PropertyType.LAND, PropertyType.rightMoveToPropertyType(107));
        assertEquals(PropertyType.LAND, PropertyType.rightMoveToPropertyType(110));
        assertEquals(PropertyType.LAND, PropertyType.rightMoveToPropertyType(241));
    }

    @Test
    public void testRightMoveToPropertyTypeFarmBarn() {
        assertEquals(PropertyType.FARM_BARN, PropertyType.rightMoveToPropertyType(43));
        assertEquals(PropertyType.FARM_BARN, PropertyType.rightMoveToPropertyType(52));
        assertEquals(PropertyType.FARM_BARN, PropertyType.rightMoveToPropertyType(68));
        assertEquals(PropertyType.FARM_BARN, PropertyType.rightMoveToPropertyType(259));
    }

    @Test
    public void testRightMoveToPropertyTypeOther() {
        assertEquals(PropertyType.OTHER, PropertyType.rightMoveToPropertyType(0));
        assertEquals(PropertyType.OTHER, PropertyType.rightMoveToPropertyType(129));
        assertEquals(PropertyType.OTHER, PropertyType.rightMoveToPropertyType(256));
    }

    @Test
    public void testRightMoveToPropertyTypeSemiDetached() {
        assertEquals(PropertyType.SEMI_DETACHED, PropertyType.rightMoveToPropertyType(3));
        assertEquals(PropertyType.SEMI_DETACHED, PropertyType.rightMoveToPropertyType(14));
        assertEquals(PropertyType.SEMI_DETACHED, PropertyType.rightMoveToPropertyType(128));
    }

    @Test
    public void testRightMoveToPropertyTypeDetached() {
        assertEquals(PropertyType.DETACHED, PropertyType.rightMoveToPropertyType(4));
        assertEquals(PropertyType.DETACHED, PropertyType.rightMoveToPropertyType(15));
        assertEquals(PropertyType.DETACHED, PropertyType.rightMoveToPropertyType(21));
        assertEquals(PropertyType.DETACHED, PropertyType.rightMoveToPropertyType(131));
    }

}