package com.homethy.model.dto;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.homethy.enums.PropertyType;
import com.homethy.model.dto.ZooplaApplicantDto.ZooplaPropertyType;

import org.junit.jupiter.api.Test;

public class ZooplaApplicantDtoTest {

  @Test
  public void testToCrmPropertyTypeDetached() {
    ZooplaPropertyType type = ZooplaPropertyType.PROPERTY_TYPE_DETACHED;
    String expected = PropertyType.DETACHED.getDescription();
    String actual = ZooplaApplicantDto.ZooplaPropertyType.toCrmPropertyType(type);
    assertEquals(expected, actual);
  }

  @Test
  public void testToCrmPropertyTypeStudio() {
    ZooplaPropertyType type = ZooplaPropertyType.PROPERTY_TYPE_STUDIO;
    String expected = PropertyType.STUDIO.getDescription();
    String actual = ZooplaApplicantDto.ZooplaPropertyType.toCrmPropertyType(type);
    assertEquals(expected, actual);
  }

  @Test
  public void testToCrmPropertyTypeMidTerrace() {
    ZooplaPropertyType type = ZooplaPropertyType.PROPERTY_TYPE_MID_TERRACE;
    String expected = PropertyType.TERRACED.getDescription();
    String actual = ZooplaApplicantDto.ZooplaPropertyType.toCrmPropertyType(type);
    assertEquals(expected, actual);
  }

  @Test
  public void testToCrmPropertyTypeTownhouse() {
    ZooplaPropertyType type = ZooplaPropertyType.PROPERTY_TYPE_TOWN_HOUSE;
    String expected = PropertyType.TOWN_HOUSE.getDescription();
    String actual = ZooplaApplicantDto.ZooplaPropertyType.toCrmPropertyType(type);
    assertEquals(expected, actual);
  }

  @Test
  public void testToCrmPropertyTypeMews() {
    ZooplaPropertyType type = ZooplaPropertyType.PROPERTY_TYPE_MEWS;
    String expected = PropertyType.MEWS_HOUSE.getDescription();
    String actual = ZooplaApplicantDto.ZooplaPropertyType.toCrmPropertyType(type);
    assertEquals(expected, actual);
  }

  @Test
  public void testToCrmPropertyTypeOffice() {
    ZooplaPropertyType type = ZooplaPropertyType.PROPERTY_TYPE_OFFICE;
    String expected = PropertyType.OFFICE.getDescription();
    String actual = ZooplaApplicantDto.ZooplaPropertyType.toCrmPropertyType(type);
    assertEquals(expected, actual);
  }

  @Test
  public void testToCrmPropertyTypeTerracedBungalow() {
    ZooplaPropertyType type = ZooplaPropertyType.PROPERTY_TYPE_TERRACED_BUNGALOW;
    String expected = PropertyType.TERRACED.getDescription();
    String actual = ZooplaApplicantDto.ZooplaPropertyType.toCrmPropertyType(type);
    assertEquals(expected, actual);
  }

  @Test
  public void testToCrmPropertyTypeLAND() {
    ZooplaPropertyType type = ZooplaPropertyType.PROPERTY_TYPE_LAND;
    String expected = PropertyType.LAND.getDescription();
    String actual = ZooplaApplicantDto.ZooplaPropertyType.toCrmPropertyType(type);
    assertEquals(expected, actual);
  }

  @Test
  public void testToCrmPropertyTypeParkHome() {
    ZooplaPropertyType type = ZooplaPropertyType.PROPERTY_TYPE_PARK_HOME;
    String expected = PropertyType.MOBILE_STATIC.getDescription();
    String actual = ZooplaApplicantDto.ZooplaPropertyType.toCrmPropertyType(type);
    assertEquals(expected, actual);
  }

  @Test
  public void testToCrmPropertyTypeBlockOfFlats() {
    ZooplaPropertyType type = ZooplaPropertyType.PROPERTY_TYPE_BLOCK_OF_FLATS;
    String expected = PropertyType.BLOCK_OF_FLATS.getDescription();
    String actual = ZooplaApplicantDto.ZooplaPropertyType.toCrmPropertyType(type);
    assertEquals(expected, actual);
  }

  @Test
  public void testToCrmPropertyTypeCottage() {
    ZooplaPropertyType type = ZooplaPropertyType.PROPERTY_TYPE_COTTAGE;
    String expected = PropertyType.COTTAGE.getDescription();
    String actual = ZooplaApplicantDto.ZooplaPropertyType.toCrmPropertyType(type);
    assertEquals(expected, actual);
  }

  @Test
  public void testToCrmPropertyTypeBarnConversion() {
    ZooplaPropertyType type = ZooplaPropertyType.PROPERTY_TYPE_BARN_CONVERSION;
    String expected = PropertyType.FARM_BARN.getDescription();
    String actual = ZooplaApplicantDto.ZooplaPropertyType.toCrmPropertyType(type);
    assertEquals(expected, actual);
  }

  @Test
  public void testToCrmPropertyTypeDefault() {
    ZooplaPropertyType type = ZooplaPropertyType.PROPERTY_TYPE_UNSPECIFIED;
    String expected = PropertyType.OTHER.getDescription();
    String actual = ZooplaApplicantDto.ZooplaPropertyType.toCrmPropertyType(type);
    assertEquals(expected, actual);
  }

  @Test
  public void testToCrmPropertyTypeFlat() {
    ZooplaPropertyType type = ZooplaPropertyType.PROPERTY_TYPE_FLAT;
    String expected = PropertyType.FLAT.getDescription();
    String actual = ZooplaApplicantDto.ZooplaPropertyType.toCrmPropertyType(type);
    assertEquals(expected, actual);
  }

  @Test
  public void testToCrmPropertyTypeSemiDetached() {
    ZooplaPropertyType type = ZooplaPropertyType.PROPERTY_TYPE_SEMI_DETACHED;
    String expected = PropertyType.SEMI_DETACHED.getDescription();
    String actual = ZooplaApplicantDto.ZooplaPropertyType.toCrmPropertyType(type);
    assertEquals(expected, actual);
  }

  @Test
  public void testToCrmPropertyTypeTerraced() {
    ZooplaPropertyType type = ZooplaPropertyType.PROPERTY_TYPE_TERRACED;
    String expected = PropertyType.TERRACED.getDescription();
    String actual = ZooplaApplicantDto.ZooplaPropertyType.toCrmPropertyType(type);
    assertEquals(expected, actual);
  }

  @Test
  public void testToCrmPropertyTypeBungalow() {
    ZooplaPropertyType type = ZooplaPropertyType.PROPERTY_TYPE_BUNGALOW;
    String expected = PropertyType.BUNGALOW.getDescription();
    String actual = ZooplaApplicantDto.ZooplaPropertyType.toCrmPropertyType(type);
    assertEquals(expected, actual);
  }

  @Test
  public void testToCrmPropertyTypeMaisonette() {
    ZooplaPropertyType type = ZooplaPropertyType.PROPERTY_TYPE_MAISONETTE;
    String expected = PropertyType.MAISONETTE.getDescription();
    String actual = ZooplaApplicantDto.ZooplaPropertyType.toCrmPropertyType(type);
    assertEquals(expected, actual);
  }

  @Test
  public void testToCrmPropertyTypeEndTerrace() {
    ZooplaPropertyType type = ZooplaPropertyType.PROPERTY_TYPE_END_TERRACE;
    String expected = PropertyType.END_OF_TERRACE.getDescription();
    String actual = ZooplaApplicantDto.ZooplaPropertyType.toCrmPropertyType(type);
    assertEquals(expected, actual);
  }

  @Test
  public void testToCrmPropertyTypeDetachedBungalow() {
    ZooplaPropertyType type = ZooplaPropertyType.PROPERTY_TYPE_DETACHED_BUNGALOW;
    String expected = PropertyType.DETACHED.getDescription();
    String actual = ZooplaApplicantDto.ZooplaPropertyType.toCrmPropertyType(type);
    assertEquals(expected, actual);
  }

  @Test
  public void testToCrmPropertyTypeSemiDetachedBungalow() {
    ZooplaPropertyType type = ZooplaPropertyType.PROPERTY_TYPE_SEMI_DETACHED_BUNGALOW;
    String expected = PropertyType.SEMI_DETACHED.getDescription();
    String actual = ZooplaApplicantDto.ZooplaPropertyType.toCrmPropertyType(type);
    assertEquals(expected, actual);
  }

  @Test
  public void testToCrmPropertyTypeLinkDetached() {
    ZooplaPropertyType type = ZooplaPropertyType.PROPERTY_TYPE_LINK_DETACHED;
    String expected = PropertyType.DETACHED.getDescription();
    String actual = ZooplaApplicantDto.ZooplaPropertyType.toCrmPropertyType(type);
    assertEquals(expected, actual);
  }

  @Test
  public void testToCrmPropertyTypeChalet() {
    ZooplaPropertyType type = ZooplaPropertyType.PROPERTY_TYPE_CHALET;
    String expected = PropertyType.OTHER.getDescription();
    String actual = ZooplaApplicantDto.ZooplaPropertyType.toCrmPropertyType(type);
    assertEquals(expected, actual);
  }
}