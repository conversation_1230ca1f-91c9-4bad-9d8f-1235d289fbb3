package com.homethy.controller;

import com.homethy.component.RequestContext;
import com.homethy.config.ListingHubConfig;
import com.homethy.model.common.RestResult;
import com.homethy.model.dto.RightmoveApplicantDto;
import com.homethy.schedule.RightMoveTasks;
import com.homethy.web.crm.util.JacksonUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024-09-23
 */
@RestController
@RequestMapping("/api")
public class DemoController {

  @Autowired
  private RightMoveTasks rightMoveTasks;

  @Autowired
  private RequestContext requestContext;

  @GetMapping(value = "/rightmove/lead")
  public String rightMoveNewLead(@RequestHeader("Authorization") String auth) {
    this.extractApiKey(auth);
    rightMoveTasks.getViewRecord();
    return JacksonUtils.toJson(RestResult.success(null));
  }

  @PostMapping(value = "/rightmove/lead/mock")
  public String rightMoveNewLeadMock(@RequestBody RightmoveApplicantDto applicantDto,
                                     @RequestHeader("Authorization") String auth) {
    this.extractApiKey(auth);
    requestContext.setData(applicantDto);
    requestContext.setMock(true);
    rightMoveTasks.getViewRecord();
    return JacksonUtils.toJson(RestResult.success(null));
  }


  // XzwM2Mjzx5FvjBEuLTPdFG728wn2rqn9dJ9Aw0M4CpE
  private void extractApiKey(String authorizationHeader) {
    String apiKey = ListingHubConfig.getProperty("rightmove.mockKey");
    String auth = "Bearer " + apiKey;
    if (!auth.equals(authorizationHeader)) {
      throw new IllegalArgumentException("Invalid Auth");
    }
  }
}
