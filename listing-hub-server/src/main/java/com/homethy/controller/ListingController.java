package com.homethy.controller;

import com.homethy.exception.ErrorCodeEnum;
import com.homethy.microservice.client.bo.p.User;
import com.homethy.microservice.mvc.HostHolder;
import com.homethy.microservice.mvc.annotation.LoginRequired;
import com.homethy.model.ListingIntegrationInfo;
import com.homethy.model.ListingUpdateInfo;
import com.homethy.model.common.RestResult;
import com.homethy.model.dto.CheckRepeatPropertyResult;
import com.homethy.model.dto.ListingIntegrationInfoReq;
import com.homethy.model.dto.ListingIntegrationInfoResp;
import com.homethy.model.dto.ListingViewsResp;
import com.homethy.model.dto.PostPropertyResult;
import com.homethy.model.dto.SearchCondition;
import com.homethy.model.dto.ThirdPartyListingUpdateReq;
import com.homethy.service.ListingService;
import com.homethy.util.JsonUtil;
import com.homethy.web.crm.util.JacksonUtils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-09-25
 */
@RestController
@RequestMapping("/listing")
public class ListingController {

  private static final Logger logger = LoggerFactory.getLogger(ListingController.class);

  @Autowired
  private ListingService listingService;

  @Autowired
  private HostHolder hostHolder;

  @LoginRequired
  @PostMapping(value = "/postProperty")
  public String postProperty(@RequestBody Map<String, Object> params) {
    try {
      params.put("currentUserId", hostHolder.getUserId());
      params.put("teamId", hostHolder.getUser().getTeamId());
      String userName = hostHolder.getUser().getFirstName() + hostHolder.getUser().getLastName();
      params.put("currentUserName", userName);
      params.put("mlsOrgId", String.valueOf(hostHolder.getUser().getTeamId()));
      PostPropertyResult result = listingService.postProperty(params);
      return JacksonUtils.toJson(RestResult.success(result));
    } catch (Exception e) {
      logger.error("postProperty error:params={}", params, e);
      return JacksonUtils.toJson(RestResult.failure(e.getMessage()));
    }
  }

  @LoginRequired
  @DeleteMapping (value = "/deleteProperty/{id}")
  public String deleteProperty(@PathVariable long id,
                               @RequestParam("marketStatus") int marketStatus) {
    try {
      User user = hostHolder.getUser();
      listingService.deleteProperty(user, id, marketStatus);
      return JacksonUtils.toJson(RestResult.success(null));
    } catch (Exception e) {
      logger.error("deleteProperty error:id={}", id, e);
      return JacksonUtils.toJson(RestResult.failure(e.getMessage()));
    }
  }

  @LoginRequired
  @PostMapping(value = "/checkRepeatProperty")
  public String checkRepeatProperty(@RequestBody Map<String, Object> params) {
    try {
      params.put("currentUserId", hostHolder.getUserId());
      params.put("mlsOrgId", String.valueOf(hostHolder.getUser().getTeamId()));
      CheckRepeatPropertyResult result = listingService.checkRepeatProperty(params);
      return JacksonUtils.toJson(RestResult.success(result));
    } catch (Exception e) {
      logger.error("checkRepeatProperty error:params={}", params, e);
      return JacksonUtils.toJson(RestResult.failure(e.getMessage()));
    }
  }

  @LoginRequired
  @PostMapping(value = "/listHistoryEvents")
  public String getListingHistory(@RequestBody Map<String, Object> params) {
    //check Permissions
    try {
      long id = (Long)params.get("id");
      List<ListingUpdateInfo> listingHistory = listingService.getListingHistory(id);
      return JacksonUtils.toJson(RestResult.success(listingHistory));
    } catch (Exception e) {
      logger.error("getListingHistory error:params={}", params, e);
      return JacksonUtils.toJson(RestResult.failure(ErrorCodeEnum.UNKNOWN_ERROR, null));
    }
  }

  /**
   * Create or incrementally update
   */
  @LoginRequired
  @PostMapping(value = "/postListingIntegrationInfo")
  public String createOrUpdateIntegrationInfo
      (@RequestBody @Validated ListingIntegrationInfoReq integrationRequest) {
    try {
      if (integrationRequest.getOfficeId() == -1) {
        // company
        integrationRequest.setOfficeId(0L);
      }
      User user = hostHolder.getUser();
      long teamId = user.getTeamId();
      integrationRequest.setAgentId(hostHolder.getUserId());
      integrationRequest.setTeamId(teamId);
      ListingIntegrationInfo integrationInfo =
          listingService.postIntegrationInfo(integrationRequest);
      integrationRequest.setId(integrationInfo.getId());
      return JacksonUtils.toJson(RestResult.success(integrationRequest));
    } catch (Exception e) {
      logger.error("setBranchId error:req=" + integrationRequest, e);
      return JacksonUtils.toJson(RestResult.failure(ErrorCodeEnum.UNKNOWN_ERROR, null));
    }
  }

  /**
   *
   * @param officeId the officeId managed by the current user, not current user's officeId
   * @return ListingIntegrationInfoResp
   */
  @LoginRequired
  @GetMapping(value = "/getListingIntegrationInfo")
  public String getBranchId(@RequestParam long officeId) {
    try {
      if (officeId == -1) {
        // company
        officeId = 0;
      }
      User user = hostHolder.getUser();
      List<ListingIntegrationInfoResp> listingIntegrationInfos
          = listingService.listIntegrationInfo(user.getTeamId(), user.getId(), officeId);
      return JacksonUtils.toJson(RestResult.success(listingIntegrationInfos));
    } catch (Exception e) {
      logger.error("getBranchId error:officeId=" + officeId, e);
      return JacksonUtils.toJson(RestResult.failure(ErrorCodeEnum.UNKNOWN_ERROR, null));
    }
  }

  @LoginRequired
  @PostMapping(value = "/deleteListingIntegrationInfo")
  public String removeBranchId(@RequestBody Map<String, Object> map) {
    try {
      Object id = map.get("id");
      long integrationInfoId = Long.parseLong(id.toString());
      ListingIntegrationInfoResp listingIntegrationInfoResp =
          listingService.deleteIntegrationInfo(integrationInfoId, hostHolder.getUser());
      return JacksonUtils.toJson(RestResult.success(listingIntegrationInfoResp));
    } catch (Exception e) {
      logger.error("removeBranchId error:map=" + map, e);
      return JacksonUtils.toJson(RestResult.failure(ErrorCodeEnum.UNKNOWN_ERROR, null));
    }
  }

  @LoginRequired
  @PostMapping(value = "/searchProperty")
  public String searchProperty(@RequestBody SearchCondition searchCondition) {
    try {
      searchCondition.setTeamId(hostHolder.getUser().getTeamId());
      searchCondition.setUserId(hostHolder.getUser().getId());
      return listingService.searchProperty(searchCondition);
    } catch (Exception e) {
      logger.error("searchProperty error:params={}", JsonUtil.toJson(searchCondition), e);
      return JacksonUtils.toJson(RestResult.failure(e.getMessage()));
    }
  }

  @LoginRequired
  @PostMapping(value = "/updateThirdPartyInfo")
  public String updateThirdPartyInfo(@RequestBody ThirdPartyListingUpdateReq listingUpdateReq) {
    try {
      List<Integer> res = listingService.updateThirdPartyInfo(listingUpdateReq,
          hostHolder.getUser());
      return JacksonUtils.toJson(RestResult.success(res));
    } catch (Exception e) {
      logger.error("updateThirdPartyInfo error:listingUpdateReq={}", listingUpdateReq, e);
      return JacksonUtils.toJson(RestResult.failure(e.getMessage()));
    }
  }

  @LoginRequired
  @GetMapping(value = "/views/{id}")
  public String getListingViews(@PathVariable long id,
                                @RequestParam("marketStatus") int marketStatus) {
    try {
      User user = hostHolder.getUser();
      ListingViewsResp res = listingService.getListingViews(id, marketStatus, user);
      return JacksonUtils.toJson(RestResult.success(res));
    } catch (Exception e) {
      logger.error("getListingViews error:listingId={}", id, e);
      return JacksonUtils.toJson(RestResult.failure(ErrorCodeEnum.UNKNOWN_ERROR, null));
    }
  }
}
