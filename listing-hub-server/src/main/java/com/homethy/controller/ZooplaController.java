package com.homethy.controller;

import com.homethy.config.ListingHubConfig;
import com.homethy.model.common.RestResult;
import com.homethy.model.dto.ZooplaApplicantDto;
import com.homethy.service.ZooplaService;
import com.homethy.web.crm.util.JacksonUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Description
 * @create 2025-01-07 9:26
 */
@RestController
@RequestMapping("/zoopla")
@Slf4j
public class ZooplaController {

  @Autowired
  private ZooplaService zooplaService;

  @PostMapping("/newLead")
  public String createLead(@RequestBody Map<String, Object> params,
                           @RequestHeader("Authorization") String auth) {
    this.extractApiKey(auth);
    if (params.get("applicant") != null) {
      try {
        ZooplaApplicantDto zooplaApplicantDto =
            this.convertApplicantDto((Map<String, Object>)params.get("applicant"));
        log.info("createLead, applicant:{}", zooplaApplicantDto);
        zooplaService.createLead(zooplaApplicantDto);
      } catch (IOException e) {
        log.error("convertApplicantDto fail, param:{}", params);
      }
    }
    return JacksonUtils.toJson(RestResult.success(null));
  }

  private ZooplaApplicantDto convertApplicantDto(Map<String, Object> applicant) throws IOException {
    String applicantJson = JacksonUtils.map2Json(applicant);
    return JacksonUtils.fromJson(applicantJson, ZooplaApplicantDto.class);
  }

  private void extractApiKey(String authorizationHeader) {
    String apiKey = ListingHubConfig.getProperty("zoopla.apiKey");
    String auth = "Bearer " + apiKey;
    if (!auth.equals(authorizationHeader)) {
      throw new IllegalArgumentException("Invalid Auth");
    }
  }
}
