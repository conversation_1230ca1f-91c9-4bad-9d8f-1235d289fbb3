package com.homethy.advice;

import com.homethy.exception.ErrorCodeEnum;
import com.homethy.model.common.RestResult;
import com.homethy.web.crm.util.JacksonUtils;

import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.HashMap;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Description
 * @create 2024-10-24 11:03
 */
@Slf4j
@RestControllerAdvice
public class ControllerAdvice {

  @ExceptionHandler(MethodArgumentNotValidException.class)
  public String handleParamException(MethodArgumentNotValidException ex) {
    log.error("Params error: ", ex);
    Map<String, String> errData = new HashMap<>();
    for (FieldError error : ex.getBindingResult().getFieldErrors()) {
      errData.put(error.getField(), error.getDefaultMessage());
    }
    return JacksonUtils.toJson(RestResult.failure(ErrorCodeEnum.PARAMS_ERROR, errData));
  }
}
