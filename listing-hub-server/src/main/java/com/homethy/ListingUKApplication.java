package com.homethy;

import com.homethy.service.ListingHubRedisService;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 * @date 2024-09-23
 */
@SpringBootApplication
@EnableScheduling
@EnableAsync
@EnableFeignClients({
    "com.homethy.microservice",
    "com.homethy.client",
    "com.lofty.site"
})
public class ListingUKApplication {
  public static ConfigurableApplicationContext context;

  public static void main(String[] args) {
    ListingHubRedisService.setRedisName();
    System.setProperty("spring.main.allow-bean-definition-overriding", "true");
    System.setProperty("spring.main.allow-circular-references", "true");
    SpringApplication app = new SpringApplication(ListingUKApplication.class);
    context = app.run();
  }
}
