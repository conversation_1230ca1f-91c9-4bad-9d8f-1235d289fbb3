package com.homethy.model.common;

import com.homethy.exception.ErrorCodeEnum;
import com.homethy.util.MdcUtil;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024-09-25
 */
@Getter
public class RestResult<T> {
  private int code;
  private String message;
  private String traceId;
  private T data;

  public RestResult(int code, String message, T data) {
    this.code = code;
    this.message = message;
    this.data = data;
  }

  public RestResult(int code, String message, String traceId, T data) {
    this.code = code;
    this.message = message;
    this.traceId = traceId;
    this.data = data;
  }

  public static <T> RestResult<T> success(T data) {
    return new RestResult<>(200, "Success", MdcUtil.getSpanId(), data);
  }

  public static <T> RestResult<T> failure(String message) {
    return new RestResult<>(500, message, MdcUtil.getSpanId(),null);
  }

  public static <T> RestResult<T> failure(ErrorCodeEnum codeEnum, T data) {
    return new RestResult<>(500, codeEnum.getErrorMsg(), MdcUtil.getSpanId(), data);
  }

  public void setCode(int code) {
    this.code = code;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public void setData(T data) {
    this.data = data;
  }
}
