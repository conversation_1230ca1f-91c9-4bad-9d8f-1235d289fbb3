<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!--  The largest history of the log  30 day  -->
    <property name="maxHistory" value="30"/>

    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <include resource="org/springframework/boot/logging/logback/console-appender.xml"/>

    <jmxConfigurator/>
    <springProperty scope="context" name="applicationName" source="spring.application.name"/>

    <appender name="homethyout" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${LOG_PATH}/app.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- rollover daily -->
            <FileNamePattern>${LOG_PATH}/app.log.%d{yyyy-MM-dd}.%i.log</FileNamePattern>
            <!-- each file should be at most 100MB, keep 10 days worth of history -->
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>${maxHistory}</maxHistory>
        </rollingPolicy>
        <encoder class="com.homethy.logback.DesensitizationLayoutEncoder">
            <pattern>
                [%date{yyyy-MM-dd'T'HH:mm:ss.SSSXXX}]|%-5level|[${applicationName},%X{traceId:-},%X{spanId:-},%X{X-Span-Export:-}]|[%thread]|%logger{36}:%L|%msg%n
            </pattern>
        </encoder>
    </appender>

    <!--business perf log -->
    <appender name="statFileAppender_service"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${LOG_PATH}/service_perf/perfStats.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${LOG_PATH}/service_perf/perfStats.log.%d{yyyy-MM-dd}.log
            </FileNamePattern>
            <maxHistory>${maxHistory}</maxHistory>
        </rollingPolicy>
        <layout class="ch.qos.logback.classic.PatternLayout">
            <Pattern>
                %d{yyyy-MM-dd HH:mm:ss.SSS} [%p] [%c.%M:%L] - %m%n
            </Pattern>
        </layout>
    </appender>

    <!--controller perf log -->
    <appender name="statFileAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${LOG_PATH}/perf/perfStats.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${LOG_PATH}/perf/perfStats.log.%d{yyyy-MM-dd}.log</FileNamePattern>
            <maxHistory>${maxHistory}</maxHistory>
        </rollingPolicy>
        <layout class="ch.qos.logback.classic.PatternLayout">
            <Pattern>
                %d{yyyy-MM-dd HH:mm:ss.SSS} [%p] [%c.%M:%L] - %m%n
            </Pattern>
        </layout>
    </appender>

    <!--myibatis perf log -->
    <appender name="statFileAppender_myibatis"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${LOG_PATH}/myibatis_perf/perfStats.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${LOG_PATH}/myibatis_perf/perfStats.log.%d{yyyy-MM-dd}.log
            </FileNamePattern>
            <maxHistory>${maxHistory}</maxHistory>
        </rollingPolicy>
        <layout class="ch.qos.logback.classic.PatternLayout">
            <Pattern>
                %d{yyyy-MM-dd HH:mm:ss.SSS} [%p] [%c.%M:%L] - %m%n
            </Pattern>
        </layout>
    </appender>

    <!--business service perf log -->
    <appender name="CoalescingStatistics_Service"
              class="org.perf4j.logback.AsyncCoalescingStatisticsAppender">
        <!-- TimeSlice Configure how many time intervals to do a summary and write to the file  -->
        <timeSlice>10000</timeSlice>
        <appender-ref ref="statFileAppender_service"/>
    </appender>

    <appender name="CoalescingStatistics"
              class="org.perf4j.logback.AsyncCoalescingStatisticsAppender">
        <!-- TimeSlice Configure how many time intervals to do a summary and write to the file   The default value is  30000 ms -->
        <timeSlice>10000</timeSlice>
        <appender-ref ref="statFileAppender"/>
    </appender>

    <!--myibatis perf log -->
    <appender name="CoalescingStatistics_Myibatis"
              class="org.perf4j.logback.AsyncCoalescingStatisticsAppender">
        <!-- TimeSlice Configure how many time intervals to do a summary and write to the file   The default value is  30000 ms -->
        <timeSlice>10000</timeSlice>
        <appender-ref ref="statFileAppender_myibatis"/>
    </appender>

    <logger name="org.apache.kafka.clients.consumer.ConsumerConfig" level="error" additivity="false">
        <appender-ref ref="CoalescingStatistics_Service"/>
    </logger>

    <logger name="org.apache.kafka.clients.producer.ProducerConfig" level="error" additivity="false">
        <appender-ref ref="CoalescingStatistics_Service"/>
    </logger>

    <!--  performance log -->
    <logger name="org.perf4j.TimingLogger" level="info" additivity="false">
        <appender-ref ref="CoalescingStatistics"/>
    </logger>

    <!-- myibatis perf log -->
    <logger name="myibatis.perf.logger" level="info" additivity="false">
        <appender-ref ref="CoalescingStatistics_Myibatis"/>
    </logger>

    <!-- business perf log -->
    <logger name="service.perf.logger" level="info" additivity="false">
        <appender-ref ref="CoalescingStatistics_Service"/>
    </logger>

    <!-- myibatis If you call there, use the call class to get the logger reference :http://mybatis.org/mybatis-3/zh/logging.html -->
    <logger name="com.homethy.persistence.dao" level="info" additivity="false">
        <appender-ref ref="homethyout"/>
    </logger>

    <logger name="org.mybatis.spring" level="error" additivity="false">
        <appender-ref ref="homethyout"/>
    </logger>

    <logger name="com.homethy" level="info" additivity="false">
        <appender-ref ref="homethyout"/>
    </logger>

    <logger name="org.springframework" level="info" additivity="false">
        <appender-ref ref="homethyout"/>
    </logger>
    <logger name="org.simplejavamail.mailer.internal.mailsender" level="OFF"
            additivity="false">
    </logger>
    <root>
        <level value="info"/>
        <appender-ref ref="homethyout"/>
    </root>
</configuration>
