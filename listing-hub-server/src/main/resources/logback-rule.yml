homethy:
  logback:
    rule:
      custom: [ ]
      regex:
        #        email
        - key: (\w){1,4}@
          value: \****@
        #        phone
#        - key: ([^0-9]{1})([0-9]{3})([0-9]{3,4})([0-9]{4})([^0-9]{1}|$)
#          value: $1$2****$4$5
        - key: ([0-9]{3})(-)([0-9]{3})(-)([0-9]{4})
          value: $1$2****$4$5
        #        name
        - key: (name=|fromName":")([a-z,A-Z]{1,20}\b)
          value: $1****