#  split the application into layers to be added
FROM harbor.d.chime.me/common/chime-jdk:v17-01 as builder
WORKDIR /opt/app

# copy application.jar
ARG JAR_FILE=build/libs/app.jar
COPY ${JAR_FILE} application.jar
RUN java -Djarmode=layertools -jar application.jar extract

# build app image
FROM harbor.d.chime.me/common/chime-jdk:v17-01
WORKDIR /opt/app

COPY --from=builder /opt/app/dependencies/ ./
COPY --from=builder /opt/app/spring-boot-loader/ ./
COPY --from=builder /opt/app/snapshot-dependencies/ ./
COPY --from=builder /opt/app/application/ ./

# start script
ENTRYPOINT ["/opt/app/run.sh"]