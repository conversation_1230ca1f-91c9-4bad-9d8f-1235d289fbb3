plugins {
    id "java-library"
    id "maven-publish"
    id 'org.springframework.boot' version '3.1.0' apply false
    id "jacoco"
    id "org.sonarqube" version "2.7"
    id "com.gorylenko.gradle-git-properties" version "2.4.0"
    id "io.spring.dependency-management" version "1.1.4" apply false
    id 'checkstyle'
    id "com.github.spotbugs" version "4.4.4"
    id "application"
}

allprojects {
    group = 'com.homethy'
    apply plugin: 'java-library'
    apply plugin: 'maven-publish'
    apply plugin: 'jacoco'
    apply plugin: 'checkstyle'
    apply plugin: 'com.github.spotbugs'
    buildscript {
        repositories {
            maven { url "https://nexus.d.chime.me/repository/public-snapshots/" }
        }
    }
    repositories {
        mavenLocal()
        maven {
            url = 'https://nexus.d.chime.me/repository/public-snapshots/'
        }
    }

    checkstyle {
        toolVersion "8.35"
        config project.resources.text.fromUri("https://gitlab.w.chime." +
                "me/chime/checkstyle/raw/master/checkstyle.xml")
    }

    spotbugs {
        toolVersion = '4.0.6'
    }

    spotbugsMain {
        reports {
            xml.enabled false
            html.enabled true
        }
    }

    tasks.withType(Checkstyle) {
        reports {
            xml.required = false
            html.required = true
        }
    }

    test {
        ignoreFailures = true
    }
}

subprojects {
    sourceCompatibility = '17'
    targetCompatibility = '17'

    compileJava.options.fork = true

    dependencies {
        implementation platform(
                "org.springframework.boot:spring-boot-dependencies:${spring_boot_version}")
        implementation platform(
                "org.springframework.cloud:spring-cloud-dependencies:${spring_cloud_version}") {
            exclude group: "org.apache.curator", module: "curator-recipes"
            exclude group: "org.apache.zookeeper", module: "zookeeper"
            exclude group: "org.apache.curator", module: "curator-framework"
            exclude group: "mysql", module: "mysql-connector-java"
            exclude group: "redis.clients", module: "jedis"
        }
        implementation 'org.springframework.boot:spring-boot-starter-web:3.2.4'

        implementation "org.projectlombok:lombok:${lombok_version}"
        annotationProcessor "org.projectlombok:lombok:${lombok_version}"
        testImplementation "org.projectlombok:lombok:${lombok_version}"
        testAnnotationProcessor "org.projectlombok:lombok:${lombok_version}"
        //for test
        testImplementation 'junit:junit'
        testImplementation 'com.homethy:jmockit:1.4.0-SNAPSHOT'
    }
    tasks.withType(JavaCompile) {
        options.encoding = 'UTF-8'
    }
}
