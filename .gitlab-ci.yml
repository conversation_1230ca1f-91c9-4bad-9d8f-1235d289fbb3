include:
  - project: "devops/gitlab-ci-template"
    file: "/template/**********************.yaml"
    ref: master
  - project: "devops/gitlab-ci-template"
    file: "/template/java-gradle-lambda-aws.yaml"
    ref: master

variables:
  APP_NAMESPACE: data
  APP_NAME: listing-hub

stages:
  - server

# -------------------- service -----------------------

server/check:
  stage: server
  extends: .check
  when: manual
  needs: []

server/build:
  stage: server
  image: ${DOCKER_REGISTRY_DOMAIN}/docker/gradle:8.7.0-jdk21-alpine
  needs:
    - server/check
  extends: .gradle-build
  script:
    - gradle clean :listing-hub-server:build -x check -x spotbugsMain -x test -x spotbugsTest --refresh-dependencies --parallel

server/image:
  stage: server
  extends: .build-push-docker-image
  needs:
    - server/build
  variables:
    DOCKER_DIR: listing-hub-server
    APP_NAME: listing-hub-server

server/deploy:
  stage: server
  needs:
    - server/image
  extends: .eks-deploy
  variables:
    APP_NAME: listing-hub-server

server/notify:
  stage: server
  needs:
    - server/deploy
  extends: .notify
  variables:
    APP_NAME: listing-hub-server