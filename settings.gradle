/*
 * This file was generated by the Gradle 'init' task.
 *
 * The settings file is used to specify which projects to include in your build.
 *
 * Detailed information about configuring a multi-project build in Gradle can be found
 * in the user manual at https://docs.gradle.org/7.4.2/userguide/multi_project_builds.html
 */
pluginManagement {
    repositories {
        maven {
            url 'https://nexus.d.chime.me/repository/gradle-plugin/'
        }
        gradlePluginPortal()
    }
}

rootProject.name = 'listing-hub'
include 'listing-hub-client'
include 'listing-hub-server'
include 'listing-hub-service'

